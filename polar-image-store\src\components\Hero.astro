---
export interface Props {
  title: string;
  subtitle?: string;
}

const {
  title,
  subtitle
} = Astro.props;
---

<section class="relative py-12 lg:py-20 bg-gradient-to-br from-white via-primary-50/30 to-accent-50/20 overflow-hidden">
  <!-- Background decoration -->
  <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>
  <div class="absolute top-20 right-20 w-72 h-72 bg-accent-200/20 rounded-full blur-3xl"></div>
  <div class="absolute bottom-20 left-20 w-96 h-96 bg-primary-200/20 rounded-full blur-3xl"></div>
  
  <div class="container relative">
    <div class="max-w-4xl mx-auto">
      <!-- 1. Main title -->
      <h1 class="text-3xl md:text-4xl lg:text-5xl xl:text-5xl font-bold text-primary-900 mb-8 leading-tight max-w-3xl text-center" set:html={title}>
      </h1>

      <!-- 2. Category Navigation -->
      <div class="relative mb-8">
        <div class="overflow-x-auto scrollbar-hide" id="categoryScroll">
          <div class="flex gap-2 pb-2 min-w-max justify-center">
            <slot name="category-navigation" />
          </div>
        </div>
      </div>

      <!-- 3. Search Bar -->
      <div class="relative mb-8">
        <div class="max-w-md mx-auto">
          <form id="heroSearchForm" action="/products" method="GET" class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              id="heroSearchInput"
              name="search"
              placeholder="Search for images..."
              class="block w-full pl-10 pr-4 py-2.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200"
            />
            <button
              type="submit"
              class="absolute inset-y-0 right-0 flex items-center pr-3"
            >
              <span class="sr-only">Search</span>
            </button>
          </form>
        </div>
      </div>

      <!-- 4. Tag Navigation -->
      <div class="relative">
        <slot name="tag-navigation" />
      </div>
    </div>
  </div>
</section>

<style>
  .bg-grid-pattern {
    background-image: 
      linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const scrollContainer = document.getElementById('categoryScroll');
    
    if (scrollContainer) {
      
      // Enable touch scrolling for mobile devices
      let isDown = false;
      let startX;
      let scrollLeft;

      scrollContainer.addEventListener('touchstart', (e) => {
        isDown = true;
        startX = e.touches[0].pageX - scrollContainer.offsetLeft;
        scrollLeft = scrollContainer.scrollLeft;
      });

      scrollContainer.addEventListener('touchend', () => {
        isDown = false;
      });

      scrollContainer.addEventListener('touchmove', (e) => {
        if (!isDown) return;
        // Không gọi e.preventDefault() để cho phép cuộn mặc định trên thiết bị di động
        const x = e.touches[0].pageX - scrollContainer.offsetLeft;
        const walk = (x - startX) * 2; // Scroll speed multiplier
        scrollContainer.scrollLeft = scrollLeft - walk;
      });
      
      // Also enable mouse drag scrolling for desktop
      scrollContainer.addEventListener('mousedown', (e) => {
        isDown = true;
        startX = e.pageX - scrollContainer.offsetLeft;
        scrollLeft = scrollContainer.scrollLeft;
        scrollContainer.style.cursor = 'grabbing';
      });

      scrollContainer.addEventListener('mouseleave', () => {
        isDown = false;
        scrollContainer.style.cursor = 'grab';
      });

      scrollContainer.addEventListener('mouseup', () => {
        isDown = false;
        scrollContainer.style.cursor = 'grab';
      });

      scrollContainer.addEventListener('mousemove', (e) => {
        if (!isDown) return;
        e.preventDefault();
        const x = e.pageX - scrollContainer.offsetLeft;
        const walk = (x - startX) * 2; // Scroll speed multiplier
        scrollContainer.scrollLeft = scrollLeft - walk;
      });
      
      // Add grab cursor style
      scrollContainer.style.cursor = 'grab';
      
      // Category tab functionality
      const categoryTabs = document.querySelectorAll('.category-tab');
      categoryTabs.forEach(tab => {
        tab.addEventListener('click', (e) => {
          const categoryId = e.currentTarget.dataset.category;

          // Remove active class from all tabs
          categoryTabs.forEach(t => {
            t.classList.remove('bg-accent-600', 'text-white', 'shadow-md');
            t.classList.add('bg-primary-50', 'text-primary-700', 'hover:bg-primary-100', 'hover:text-primary-900');

            // Update count badge
            const badge = t.querySelector('span');
            if (badge) {
              badge.classList.remove('bg-white/20', 'text-white');
              badge.classList.add('bg-primary-200', 'text-primary-600');
            }
          });

          // Add active class to clicked tab
          tab.classList.remove('bg-primary-50', 'text-primary-700', 'hover:bg-primary-100', 'hover:text-primary-900');
          tab.classList.add('bg-accent-600', 'text-white', 'shadow-md');

          // Update count badge
          const badge = tab.querySelector('span');
          if (badge) {
            badge.classList.remove('bg-primary-200', 'text-primary-600');
            badge.classList.add('bg-white/20', 'text-white');
          }

          // Check if we're on homepage or products page
          const isHomepage = window.location.pathname === '/';

          if (isHomepage) {
            // On homepage: dispatch event for client-side filtering
            console.log('📡 Dispatching categoryChange event for homepage:', categoryId);
            window.dispatchEvent(new CustomEvent('categoryChange', {
              detail: { categoryId }
            }));
          } else {
            // On other pages: navigate to category page
            if (categoryId === 'all') {
              window.location.href = '/products';
            } else {
              window.location.href = `/products/category/${categoryId}`;
            }
          }
        });
      });

      // Tag tab functionality
      const tagTabs = document.querySelectorAll('.tag-tab');
      tagTabs.forEach(tab => {
        tab.addEventListener('click', (e) => {
          const tagId = e.currentTarget.dataset.tag;

          // Navigate to tag page
          if (tagId === 'all') {
            window.location.href = '/products';
          } else {
            window.location.href = `/products/tag/${tagId}`;
          }
        });
      });
    }

    // Hero search functionality for mobile redirect
    const heroSearchForm = document.getElementById('heroSearchForm');
    const heroSearchInput = document.getElementById('heroSearchInput');

    // Mobile detection function
    function isMobile() {
      return window.innerWidth < 768;
    }

    // Mobile search modal functionality
    function handleMobileSearchClick(input, form) {
      if (isMobile()) {
        input.blur(); // Remove focus to prevent keyboard
        // Open search modal instead of redirecting
        const currentQuery = input.value.trim();
        (window as any).openSearchModal?.(currentQuery);
      }
    }

    if (heroSearchInput && heroSearchForm) {
      // Handle focus for mobile redirect
      heroSearchInput.addEventListener('focus', (e) => {
        handleMobileSearchClick(e.target, heroSearchForm);
      });

      // Handle click for mobile redirect
      heroSearchInput.addEventListener('click', (e) => {
        handleMobileSearchClick(e.target, heroSearchForm);
      });

      // Handle form submission
      heroSearchForm.addEventListener('submit', (e) => {
        const query = heroSearchInput.value.trim();
        if (query && isMobile()) {
          e.preventDefault();
          // Open search modal with query instead of redirecting
          (window as any).openSearchModal?.(query);
        }
        // For desktop, let the form submit normally to /products
      });
    }
  });
</script>
