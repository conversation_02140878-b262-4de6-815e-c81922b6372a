---
import '../styles/global.css';
import { Image } from 'astro:assets';
import { SEO } from "astro-seo";
import SearchModal from '../components/SearchModal.astro';

export interface Props {
  title: string;
  description?: string;
  image?: string;
  canonical?: string;
  noindex?: boolean;
  type?: 'website' | 'article' | 'product';
}

const {
  title,
  description = "Beautiful digital images from our collection",
  image = "/og-image.jpg",
  canonical,
  noindex = false,
  type = 'website'
} = Astro.props;

const siteUrl = import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store';
const fullImageUrl = image.startsWith('http') ? image : `${siteUrl}${image}`;
const canonicalUrl = canonical || Astro.url.href;
---

<!doctype html>
<html lang="en">
  <head>
    <SEO
      title={title}
      description={description}
      canonical={canonicalUrl}
      noindex={noindex}
      charset="UTF-8"
      openGraph={{
        basic: {
          title: title,
          type: type,
          image: fullImageUrl,
          url: canonicalUrl
        },
        optional: {
          description: description,
          siteName: "Polar Image Store",
          locale: "en_US"
        }
      }}
      twitter={{
        card: "summary_large_image",
        site: "@polarimagestore",
        creator: "@polarimagestore",
        title: title,
        description: description,
        image: fullImageUrl,
        imageAlt: `${title} - Polar Image Store`
      }}
      extend={{
        link: [
          { rel: "icon", type: "image/svg+xml", href: "/favicon.svg" },
          { rel: "sitemap", href: "/sitemap-index.xml" },
          { rel: "canonical", href: canonicalUrl }
        ],
        meta: [
          { name: "viewport", content: "width=device-width, initial-scale=1.0" },
          { name: "generator", content: Astro.generator },
          { name: "robots", content: noindex ? "noindex, nofollow" : "index, follow" },
          { name: "googlebot", content: noindex ? "noindex, nofollow" : "index, follow" },
          { name: "theme-color", content: "#6366f1" },
          { name: "msapplication-TileColor", content: "#6366f1" }
        ]
      }}
    />
  </head>
  <body class="min-h-screen flex flex-col">
    <header class="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-primary-100 py-4">
      <div class="container">
        <nav class="flex items-center justify-between">
          <!-- Logo -->
          <a href="/" class="flex items-center gap-2 text-xl font-bold text-primary-900 hover:text-accent-600 transition-colors">
            <Image 
              src="/logo.svg" 
              alt="Logo" 
              width={32} 
              height={32} 
              class="w-8 h-8 text-accent-600" 
            />
            InfPik
          </a>

          <!-- Mobile Search Bar -->
          <div id="mobileHeaderSearchContainer" class="md:hidden relative flex-1 mx-2 opacity-0 transform -translate-y-2 transition-all duration-300 ease-in-out">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              id="mobileProductSearch"
              class="block w-full pl-8 pr-4 py-1.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200 text-sm"
              placeholder="Search for images..."
              autocomplete="off"
            />
          </div>

          <!-- Search Bar (Desktop) -->
          <div id="headerSearchContainer" class="hidden md:flex flex-1 max-w-md mx-8 opacity-0 transform -translate-y-2 transition-all duration-300 ease-in-out">
            <div class="relative w-full">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                id="productSearch"
                class="block w-full pl-10 pr-4 py-2.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200"
                placeholder="Search for images..."
                autocomplete="off"
              />
              <!-- Search results dropdown (hidden by default) -->
              <div id="searchResults" class="absolute top-full left-0 right-0 mt-1 bg-white border border-primary-200 rounded-xl shadow-lg z-50 hidden max-h-96 overflow-y-auto">
                <!-- Search results will be populated here -->
              </div>
            </div>
          </div>

          <!-- CTA Button & Mobile Menu -->
          <div class="flex items-center gap-4">
            <a href="/products" class="btn-primary hidden md:inline-flex">
              Browse Collection
            </a>

            <!-- Mobile menu button -->
            <button class="md:hidden p-2 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full transition-all" id="mobile-menu-button">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
              </svg>
            </button>
          </div>
        </nav>

        <!-- Mobile menu -->
        <div class="md:hidden hidden" id="mobile-menu">
          <div class="pt-4 pb-2 border-t border-primary-100 mt-4">
            <!-- Mobile Navigation -->
            <ul class="space-y-2">
              <li><a href="/" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Home</a></li>
              <li><a href="/products" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Products</a></li>
              <li><a href="/about" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">About</a></li>
            </ul>
            
            <!-- Legal Links -->
            <div class="mt-4 pt-4 border-t border-primary-100">
              <p class="px-4 text-xs uppercase text-primary-500 font-medium mb-2">Legal</p>
              <ul class="space-y-2">
                <li><a href="/privacy" class="block py-2 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all text-sm">Privacy Policy</a></li>
                <li><a href="/terms" class="block py-2 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all text-sm">Terms of Service</a></li>
              </ul>
            </div>
            
            <div class="mt-4 pt-4 border-t border-primary-100">
              <a href="/products" class="btn-primary w-full justify-center">
                Browse Collection
              </a>
            </div>
          </div>
        </div>
      </div>
    </header>

    <main class="flex-1 pb-12">
      <slot />
    </main>

    <footer class="bg-white border-t border-primary-100 py-12 text-primary-600">
      <div class="container">
        <div class="text-center">
          <div class="flex items-center justify-center gap-2 text-lg font-semibold text-primary-900 mb-4">
            <Image 
              src="/logo.svg" 
              alt="Logo" 
              width={24} 
              height={24} 
              class="w-6 h-6 text-accent-600" 
            />
            InfPik
          </div>
          <div class="flex justify-center gap-4 mb-4">
            <a href="/about" class="text-sm hover:text-accent-600 transition-colors">About Us</a>
            <a href="/privacy" class="text-sm hover:text-accent-600 transition-colors">Privacy Policy</a>
            <a href="/terms" class="text-sm hover:text-accent-600 transition-colors">Terms of Service</a>
          </div>
          <p class="text-sm">&copy; 2025 Polar Image Store. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <!-- Search Modal for Mobile -->
    <SearchModal />
  </body>
</html>

<script>
  // Mobile menu toggle
  document.addEventListener('DOMContentLoaded', () => {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
      mobileMenuButton.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
      });
    }

    // Header search bar scroll behavior
    const headerSearchContainer = document.getElementById('headerSearchContainer');
    const mobileHeaderSearchContainer = document.getElementById('mobileHeaderSearchContainer');
    let lastScrollY = window.scrollY;
    let ticking = false;

    function updateHeaderSearch() {
      const currentScrollY = window.scrollY;

      // Handle desktop search bar
      if (headerSearchContainer) {
        if (currentScrollY <= 50) {
          // At top of page - hide header search bar
          headerSearchContainer.classList.add('opacity-0', '-translate-y-2');
          headerSearchContainer.classList.remove('opacity-100', 'translate-y-0');
        } else {
          // Scrolled down - show header search bar
          headerSearchContainer.classList.remove('opacity-0', '-translate-y-2');
          headerSearchContainer.classList.add('opacity-100', 'translate-y-0');
        }
      }

      // Handle mobile search bar
      if (mobileHeaderSearchContainer) {
        if (currentScrollY <= 50) {
          // At top of page - hide mobile header search bar
          mobileHeaderSearchContainer.classList.add('opacity-0', '-translate-y-2');
          mobileHeaderSearchContainer.classList.remove('opacity-100', 'translate-y-0');
        } else {
          // Scrolled down - show mobile header search bar
          mobileHeaderSearchContainer.classList.remove('opacity-0', '-translate-y-2');
          mobileHeaderSearchContainer.classList.add('opacity-100', 'translate-y-0');
        }
      }

      lastScrollY = currentScrollY;
      ticking = false;
    }

    function requestTick() {
      if (!ticking) {
        requestAnimationFrame(updateHeaderSearch);
        ticking = true;
      }
    }

    // Listen for scroll events
    window.addEventListener('scroll', requestTick, { passive: true });

    // Initial check on page load
    updateHeaderSearch();

    // Search functionality
    const searchInput = document.getElementById('productSearch');
    const mobileSearchInput = document.getElementById('mobileProductSearch');
    const searchResults = document.getElementById('searchResults');

    let searchTimeout: any;

    // Mobile detection function
    function isMobile() {
      return window.innerWidth < 768;
    }

    // Mobile search modal functionality
    function handleMobileSearchClick(input: HTMLInputElement) {
      if (isMobile()) {
        input.blur(); // Remove focus to prevent keyboard
        // Open search modal instead of redirecting
        const currentQuery = input.value.trim();
        (window as any).openSearchModal?.(currentQuery);
      }
    }

    // Real search functionality with API
    async function handleSearch(input: any) {
      const query = input.value.trim();

      // Clear previous timeout
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }

      if (query.length > 2) {
        // Show search results dropdown with loading
        if (searchResults) {
          searchResults.classList.remove('hidden');
          searchResults.innerHTML = `
            <div class="p-4 text-center text-primary-600">
              <div class="flex items-center justify-center gap-2">
                <svg class="animate-spin w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span class="text-sm">Searching All images for "${query}"...</span>
              </div>
            </div>
          `;

          // Debounce search API call for tags
          searchTimeout = setTimeout(async () => {
            try {
              const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
              const data = await response.json();

              if (searchResults && !searchResults.classList.contains('hidden')) {
                if (data.results && data.results.length > 0) {
                  const resultsHtml = data.results.map((tag: any) => `
                    <button onclick="window.location.href='${tag.url}'" class="block w-full p-3 hover:bg-primary-50 rounded-lg transition-colors border-b border-primary-100 last:border-b-0 text-left">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                          <div class="w-8 h-8 bg-accent-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                            </svg>
                          </div>
                          <div class="flex-1 min-w-0">
                            <div class="text-primary-900 font-medium truncate">${tag.displayName}</div>
                            <div class="text-primary-600 text-sm">${tag.count} ${tag.count === 1 ? 'product' : 'products'}</div>
                          </div>
                        </div>
                        <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </button>
                  `).join('');

                  searchResults.innerHTML = `
                    <div class="p-2">
                      <div class="text-xs text-primary-500 px-3 py-2 border-b border-primary-100">
                        Search All images for "${query}"
                      </div>
                      ${resultsHtml}
                      ${data.total > data.results.length ? `
                        <div class="p-3 border-t border-primary-100">
                          <div class="text-center text-primary-600 text-sm">
                            Showing ${data.results.length} of ${data.total} tags
                          </div>
                        </div>
                      ` : ''}
                    </div>
                  `;
                } else {
                  searchResults.innerHTML = `
                    <div class="p-4 text-center">
                      <div class="text-primary-600 mb-2">No tags found for "${query}"</div>
                      <a href="/products" class="text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors">
                        Browse all products →
                      </a>
                    </div>
                  `;
                }
              }
            } catch (error) {
              console.error('Tag search error:', error);
              if (searchResults && !searchResults.classList.contains('hidden')) {
                searchResults.innerHTML = `
                  <div class="p-4 text-center text-red-600">
                    <div class="text-sm">Search failed. Please try again.</div>
                  </div>
                `;
              }
            }
          }, 300); // 300ms debounce
        }
      } else {
        // Hide search results
        if (searchResults) {
          searchResults.classList.add('hidden');
        }
      }
    }

    // Add search event listeners
    if (searchInput) {
      // Handle focus for mobile redirect
      searchInput.addEventListener('focus', (e: any) => {
        handleMobileSearchClick(e.target);
      });

      // Handle click for mobile redirect
      searchInput.addEventListener('click', (e: any) => {
        handleMobileSearchClick(e.target);
      });

      // Desktop search functionality
      searchInput.addEventListener('input', (e: any) => {
        if (!isMobile()) {
          handleSearch(e.target);
        }
      });

      searchInput.addEventListener('keydown', (e: any) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          const query = e.target.value.trim();
          if (query) {
            if (isMobile()) {
              // Open search modal with query instead of redirecting
              (window as any).openSearchModal?.(query);
            } else {
              window.location.href = `/products?search=${encodeURIComponent(query)}`;
            }
          }
        }
      });
    }

    if (mobileSearchInput) {
      // Handle focus for mobile redirect
      mobileSearchInput.addEventListener('focus', (e: any) => {
        handleMobileSearchClick(e.target);
      });

      // Handle click for mobile redirect
      mobileSearchInput.addEventListener('click', (e: any) => {
        handleMobileSearchClick(e.target);
      });

      mobileSearchInput.addEventListener('keydown', (e: any) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          const query = e.target.value.trim();
          if (query) {
            // Open search modal with query instead of redirecting
            (window as any).openSearchModal?.(query);
          }
        }
      });
    }

    // Hide search results when clicking outside
    document.addEventListener('click', (e: any) => {
      if (searchResults && !searchInput?.contains(e.target as Node) && !searchResults.contains(e.target as Node)) {
        searchResults.classList.add('hidden');
      }
    });
  });
</script>
