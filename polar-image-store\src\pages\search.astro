---
import Layout from '../layouts/Layout.astro';
import { createPolarClient, transformPolarProduct, extractUniqueTags, getTagDisplayName } from '../utils/polar';
import type { LocalProduct } from '../types/polar';

export const prerender = false;

// Get search query from URL params
const url = Astro.url;
const searchQuery = url.searchParams.get('q') || '';



let searchResults: Array<{id: string, name: string, displayName: string, count: number, url: string}> = [];
let error: string | null = null;

// Search for tags if there's a search query (using same logic as desktop)
if (searchQuery.trim()) {
  try {
    const polar = createPolarClient();
    const organizationId = import.meta.env.POLAR_ORGANIZATION_ID;

    if (organizationId) {
      const response = await polar.products.list({
        organizationId,
        isArchived: false
      });

      const productList = response.result?.items || [];
      const products = productList
        .map(transformPolarProduct)
        .filter((product): product is LocalProduct => product !== null);

      // Extract all unique tags (same as desktop)
      const allTags = extractUniqueTags(products);
      const searchTerm = searchQuery.toLowerCase().trim();

      // Filter tags based on search query (same as desktop)
      const matchingTags = allTags.filter(tag => {
        const tagName = getTagDisplayName(tag).toLowerCase();
        const tagId = tag.toLowerCase();

        // Match against both tag ID and display name
        return tagId.includes(searchTerm) || tagName.includes(searchTerm);
      });

      // Count products for each matching tag and create results (same as desktop)
      const tagResults = matchingTags.map(tag => {
        const productCount = products.filter(product =>
          product.tags && product.tags.includes(tag)
        ).length;

        return {
          id: tag,
          name: getTagDisplayName(tag),
          displayName: `#${getTagDisplayName(tag)}`,
          count: productCount,
          url: `/products/tag/${tag}`
        };
      });

      // Sort by relevance (same as desktop)
      tagResults.sort((a, b) => {
        const aExactMatch = a.name.toLowerCase() === searchTerm || a.id.toLowerCase() === searchTerm;
        const bExactMatch = b.name.toLowerCase() === searchTerm || b.id.toLowerCase() === searchTerm;

        if (aExactMatch && !bExactMatch) return -1;
        if (!aExactMatch && bExactMatch) return 1;

        // If both or neither are exact matches, sort by product count (descending)
        if (b.count !== a.count) {
          return b.count - a.count;
        }

        // Finally, sort alphabetically
        return a.name.localeCompare(b.name);
      });

      searchResults = tagResults;
    } else {
      error = 'Organization ID not configured';
    }
  } catch (err) {
    console.error('Search error:', err);
    error = 'Failed to search tags';
  }
}

// Get popular tags for suggestions (using same logic as desktop)
let popularTags: Array<{id: string, name: string, count: number}> = [];
try {
  const polar = createPolarClient();
  const organizationId = import.meta.env.POLAR_ORGANIZATION_ID;

  if (organizationId) {
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });

    const productList = response.result?.items || [];
    const products = productList
      .map(transformPolarProduct)
      .filter((product): product is LocalProduct => product !== null);

    // Extract all unique tags
    const allTags = extractUniqueTags(products);

    // Count products for each tag
    const tagResults = allTags.map(tag => {
      const productCount = products.filter(product =>
        product.tags && product.tags.includes(tag)
      ).length;

      return {
        id: tag,
        name: getTagDisplayName(tag),
        count: productCount
      };
    });

    // Sort by count and take top 10
    popularTags = tagResults
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }
} catch (err) {
  console.error('Failed to fetch popular tags:', err);
}
---

<Layout
  title={searchQuery ? `Search: ${searchQuery} - Polar Image Store` : 'Search - Polar Image Store'}
  description="Search our collection of premium digital images and artwork"
  canonical={`${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/search`}
>
  <div class="min-h-screen bg-white">
    <!-- Header with back button -->
    <div class="sticky top-0 z-50 bg-white border-b border-gray-200">
      <div class="flex items-center px-4 py-3">
        <button 
          id="backButton"
          class="mr-3 p-2 -ml-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors"
          aria-label="Go back"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        
        <!-- Search Input -->
        <div class="flex-1 relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            type="text"
            id="searchInput"
            value={searchQuery}
            placeholder="Search for images..."
            class="block w-full pl-10 pr-4 py-3 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200 text-lg"
            autocomplete="off"
            autofocus
          />
          
          <!-- Clear button -->
          <button
            id="clearButton"
            class="absolute inset-y-0 right-0 pr-3 flex items-center text-primary-400 hover:text-primary-600 transition-colors duration-200"
            style="display: none;"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="px-4 py-6">
      {searchQuery ? (
        <!-- Search Results -->
        <div>
          <div class="mb-6">
            <p class="text-gray-600">
              {searchResults.length > 0
                ? `Found ${searchResults.length} tag${searchResults.length === 1 ? '' : 's'} for "${searchQuery}"`
                : `No results for "${searchQuery}"`
              }
            </p>
          </div>

          {error ? (
            <!-- Error State - Matching Desktop Style -->
            <div class="bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden">
              <div class="p-4 text-center text-red-600">
                <div class="text-sm">{error}. Please try again.</div>
              </div>
            </div>
          ) : searchResults.length > 0 ? (
            <!-- Tags Results - Matching Desktop Dropdown Style -->
            <div class="bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden">
              <!-- Header Section -->
              <div class="text-xs text-primary-500 px-3 py-2 border-b border-primary-100 bg-gray-50">
                Search All images for "{searchQuery}"
              </div>

              <!-- Results List -->
              <div class="p-2">
                {searchResults.map((result, index) => (
                  <a
                    href={result.url}
                    class={`block w-full p-3 hover:bg-primary-50 rounded-lg transition-colors text-left ${
                      index < searchResults.length - 1 ? 'border-b border-primary-100' : ''
                    }`}
                  >
                    <div class="flex items-center justify-between">
                      <div class="flex items-center gap-3">
                        <div class="w-8 h-8 bg-accent-100 rounded-full flex items-center justify-center">
                          <svg class="w-4 h-4 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                          </svg>
                        </div>
                        <div class="flex-1 min-w-0">
                          <div class="text-primary-900 font-medium truncate">{result.displayName}</div>
                          <div class="text-primary-600 text-sm">{result.count} {result.count === 1 ? 'product' : 'products'}</div>
                        </div>
                      </div>
                      <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </a>
                ))}
              </div>

              <!-- Footer Section (if needed) -->
              {searchResults.length >= 8 && (
                <div class="p-3 border-t border-primary-100 bg-gray-50">
                  <div class="text-center text-primary-600 text-sm">
                    Showing {searchResults.length} results
                  </div>
                </div>
              )}
            </div>
          ) : (
            <!-- No Results - Matching Desktop Style -->
            <div class="bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden">
              <div class="p-4 text-center">
                <div class="text-primary-600 mb-2">No results for "{searchQuery}"</div>
                <a href="/products" class="text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors">
                  Browse all products →
                </a>
              </div>

              <!-- Popular Search Suggestions -->
              {popularTags.length > 0 && (
                <div class="border-t border-primary-100 p-4">
                  <p class="text-sm font-medium text-primary-900 mb-3 text-center">Try these searches:</p>
                  <div class="flex flex-wrap gap-2 justify-center">
                    {popularTags.slice(0, 6).map((tag) => (
                      <button
                        class="tag-suggestion px-3 py-1 bg-primary-50 text-primary-700 rounded-full text-sm hover:bg-primary-100 transition-colors border border-primary-200"
                        data-tag={tag.id}
                      >
                        #{tag.name}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      ) : (
        <!-- Initial Search State -->
        <div>
          <!-- Recent Searches - Matching Desktop Style -->
          <div id="recentSearches" class="mb-8" style="display: none;">
            <div class="flex items-center justify-between mb-4">
              <h2 class="text-lg font-semibold text-primary-900">Recent Searches</h2>
              <button
                id="clearRecentSearches"
                class="p-2 text-primary-400 hover:text-primary-600 hover:bg-primary-50 rounded-full transition-all duration-200"
                title="Clear recent searches"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
            <div id="recentSearchesList" class="bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden">
              <!-- Recent searches will be populated here with desktop-style items -->
            </div>
          </div>

          <!-- Popular Tags - Matching Desktop Style -->
          {popularTags.length > 0 && (
            <div class="mb-8">
              <h2 class="text-lg font-semibold text-primary-900 mb-4">Popular Searches</h2>
              <div class="bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden">
                <div class="p-2">
                  {popularTags.map((tag, index) => (
                    <button
                      class={`tag-suggestion block w-full p-3 hover:bg-primary-50 rounded-lg transition-colors text-left ${
                        index < popularTags.length - 1 ? 'border-b border-primary-100' : ''
                      }`}
                      data-tag={tag.id}
                    >
                      <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                          <div class="w-8 h-8 bg-accent-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                            </svg>
                          </div>
                          <div class="flex-1 min-w-0">
                            <div class="text-primary-900 font-medium truncate">#{tag.name}</div>
                            <div class="text-primary-600 text-sm">{tag.count} {tag.count === 1 ? 'product' : 'products'}</div>
                          </div>
                        </div>
                        <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          <!-- Quick Actions -->
          <div>
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Browse</h2>
            <div class="space-y-3">
              <a
                href="/products"
                class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <span class="font-medium text-gray-900">All Products</span>
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      )}
    </div>
  </div>
</Layout>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const searchInput = document.getElementById('searchInput') as HTMLInputElement;
    const clearButton = document.getElementById('clearButton') as HTMLButtonElement;
    const backButton = document.getElementById('backButton') as HTMLButtonElement;
    const tagSuggestions = document.querySelectorAll('.tag-suggestion');
    const recentSearchesContainer = document.getElementById('recentSearches');
    const recentSearchesList = document.getElementById('recentSearchesList');
    const clearRecentSearchesButton = document.getElementById('clearRecentSearches');

    let searchTimeout: any;

    // Back button functionality - always go to homepage
    if (backButton) {
      backButton.addEventListener('click', () => {
        window.location.href = '/';
      });
    }

    // Search input functionality
    if (searchInput) {
      // Auto-focus on mobile
      if (window.innerWidth < 768) {
        setTimeout(() => {
          searchInput.focus();
        }, 100);
      }

      // Show/hide clear button
      const toggleClearButton = () => {
        if (clearButton) {
          clearButton.style.display = searchInput.value.trim() ? 'flex' : 'none';
        }
      };

      searchInput.addEventListener('input', () => {
        toggleClearButton();

        // Clear previous timeout
        if (searchTimeout) {
          clearTimeout(searchTimeout);
        }

        // Check if input is empty and URL has query params
        const query = searchInput.value.trim();
        if (query === '') {
          // Input is empty - sync URL to clean state immediately
          syncUrlToCleanState();
        } else if (query.length >= 2) {
          // Debounced search for non-empty queries
          searchTimeout = setTimeout(() => {
            performSearch(query);
          }, 500);
        }
      });

      searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          const query = searchInput.value.trim();
          if (query) {
            performSearch(query);
          } else {
            // Empty query on Enter - navigate to clean search page
            navigateToCleanSearch();
          }
        } else if (e.key === 'Escape') {
          // Clear search and navigate to clean search page
          e.preventDefault();
          navigateToCleanSearch();
        }
      });

      // Initial toggle
      toggleClearButton();
    }

    // Clear button functionality - navigate to clean search page
    if (clearButton) {
      clearButton.addEventListener('click', () => {
        navigateToCleanSearch();
      });
    }

    // Clear recent searches functionality
    if (clearRecentSearchesButton) {
      clearRecentSearchesButton.addEventListener('click', () => {
        try {
          localStorage.removeItem('recentSearches');
          if (recentSearchesContainer) {
            recentSearchesContainer.style.display = 'none';
          }
        } catch (error) {
          console.error('Failed to clear recent searches:', error);
        }
      });
    }

    // Tag suggestion functionality
    tagSuggestions.forEach(button => {
      button.addEventListener('click', () => {
        const tag = button.getAttribute('data-tag');
        if (tag) {
          searchInput.value = tag;
          performSearch(tag);
        }
      });
    });

    // Navigate to clean search page
    function navigateToCleanSearch() {
      window.location.href = '/search';
    }

    // Sync URL to clean state without page reload (for immediate feedback)
    function syncUrlToCleanState() {
      if (window.location.search) {
        window.history.replaceState({}, '', '/search');
      }
    }

    // Search functionality with proper history management
    function performSearch(query: string) {
      // Save to recent searches
      saveRecentSearch(query);

      // Use replaceState to avoid creating new history entries for each search
      const newUrl = `/search?q=${encodeURIComponent(query)}`;
      window.history.replaceState({ query }, '', newUrl);

      // Reload page to show search results
      window.location.reload();
    }

    // Recent searches functionality
    function saveRecentSearch(query: string) {
      try {
        const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
        const filtered = recent.filter((item: string) => item !== query);
        filtered.unshift(query);
        const limited = filtered.slice(0, 5);
        localStorage.setItem('recentSearches', JSON.stringify(limited));
      } catch (error) {
        console.error('Failed to save recent search:', error);
      }
    }

    function loadRecentSearches() {
      try {
        const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
        if (recent.length > 0 && recentSearchesContainer && recentSearchesList) {
          recentSearchesContainer.style.display = 'block';
          recentSearchesList.innerHTML = `
            <div class="p-2">
              ${recent.map((query: string, index: number) => `
                <button class="recent-search-item block w-full p-3 hover:bg-primary-50 rounded-lg transition-colors text-left ${
                  index < recent.length - 1 ? 'border-b border-primary-100' : ''
                }" data-query="${query}">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div class="flex-1 min-w-0">
                        <div class="text-primary-900 font-medium truncate">${query}</div>
                        <div class="text-primary-600 text-sm">Recent search</div>
                      </div>
                    </div>
                    <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </button>
              `).join('')}
            </div>
          `;

          // Add click listeners to recent search items
          document.querySelectorAll('.recent-search-item').forEach(item => {
            item.addEventListener('click', () => {
              const query = item.getAttribute('data-query');
              if (query) {
                searchInput.value = query;
                performSearch(query);
              }
            });
          });
        }
      } catch (error) {
        console.error('Failed to load recent searches:', error);
      }
    }

    // Load recent searches on initial load (only if no search query)
    if (!searchInput.value.trim()) {
      loadRecentSearches();
    }
  });
</script>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
