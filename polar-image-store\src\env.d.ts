/// <reference path="../.astro/types.d.ts" />
/// <reference types="astro/client" />

// Cloudflare runtime types
type Runtime = import('@astrojs/cloudflare').Runtime<Env>;

declare namespace App {
  interface Locals extends Runtime {
    // Add any additional locals here
  }
}

// Cloudflare environment interface
interface Env {
  // Add your Cloudflare bindings here
  // Example:
  // MY_KV: KVNamespace;
  // MY_DURABLE_OBJECT: DurableObjectNamespace;
  // MY_BUCKET: R2Bucket;
  // MY_QUEUE: Queue;
  
  // Environment variables
  // PUBLIC_SITE_URL?: string;
}
