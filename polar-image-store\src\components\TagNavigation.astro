---
export interface Tag {
  id: string;
  name: string;
  count?: number;
}

export interface Props {
  tags: Tag[];
  activeTag?: string;
}

const { 
  tags,
  activeTag = 'all'
} = Astro.props;
---

<section class="py-6 bg-gray-50 border-b border-gray-200">
  <div class="container">
    <div class="mb-4">
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Browse by Tags</h3>
      <p class="text-sm text-gray-600">Find specific content with detailed tags</p>
    </div>
    
    <!-- Tag Navigation -->
    <div class="relative">
      <!-- Scroll container -->
      <div class="overflow-x-auto scrollbar-hide" id="tagScroll">
        <div class="flex gap-2 pb-2 min-w-max">
          {tags.map((tag) => (
            <button 
              class={`tag-tab flex items-center gap-2 px-3 py-1.5 rounded-full text-xs font-medium transition-all whitespace-nowrap ${
                activeTag === tag.id 
                  ? 'bg-blue-600 text-white shadow-md' 
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-100 hover:text-gray-900'
              }`}
              data-tag={tag.id}
            >
              {tag.name}
              {tag.count && (
                <span class={`text-xs px-1.5 py-0.5 rounded-full ${
                  activeTag === tag.id 
                    ? 'bg-white/20 text-white' 
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {tag.count}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>
      
      <!-- Scroll indicators -->
      <div class="absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-gray-50 to-transparent pointer-events-none opacity-0" id="leftFade"></div>
      <div class="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-gray-50 to-transparent pointer-events-none opacity-0" id="rightFade"></div>
    </div>
  </div>
</section>

<style>
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const scrollContainer = document.getElementById('tagScroll');
    const leftFade = document.getElementById('leftFade');
    const rightFade = document.getElementById('rightFade');
    
    if (scrollContainer && leftFade && rightFade) {
      // Update scroll indicators
      function updateScrollIndicators() {
        const { scrollLeft, scrollWidth, clientWidth } = scrollContainer;
        
        // Show left fade if scrolled right
        leftFade.style.opacity = scrollLeft > 0 ? '1' : '0';
        
        // Show right fade if can scroll more
        rightFade.style.opacity = scrollLeft < scrollWidth - clientWidth ? '1' : '0';
      }
      
      // Initial check
      updateScrollIndicators();
      
      // Update on scroll
      scrollContainer.addEventListener('scroll', updateScrollIndicators);
      
      // Update on resize
      window.addEventListener('resize', updateScrollIndicators);
      
      // Enable touch scrolling for mobile devices
      let isDown = false;
      let startX;
      let scrollLeft;

      scrollContainer.addEventListener('touchstart', (e) => {
        isDown = true;
        startX = e.touches[0].pageX - scrollContainer.offsetLeft;
        scrollLeft = scrollContainer.scrollLeft;
      });

      scrollContainer.addEventListener('touchend', () => {
        isDown = false;
      });

      scrollContainer.addEventListener('touchmove', (e) => {
        if (!isDown) return;
        const x = e.touches[0].pageX - scrollContainer.offsetLeft;
        const walk = (x - startX) * 2; // Scroll speed multiplier
        scrollContainer.scrollLeft = scrollLeft - walk;
      });
      
      // Also enable mouse drag scrolling for desktop
      scrollContainer.addEventListener('mousedown', (e) => {
        isDown = true;
        startX = e.pageX - scrollContainer.offsetLeft;
        scrollLeft = scrollContainer.scrollLeft;
        scrollContainer.style.cursor = 'grabbing';
      });

      scrollContainer.addEventListener('mouseleave', () => {
        isDown = false;
        scrollContainer.style.cursor = 'grab';
      });

      scrollContainer.addEventListener('mouseup', () => {
        isDown = false;
        scrollContainer.style.cursor = 'grab';
      });

      scrollContainer.addEventListener('mousemove', (e) => {
        if (!isDown) return;
        e.preventDefault();
        const x = e.pageX - scrollContainer.offsetLeft;
        const walk = (x - startX) * 2; // Scroll speed multiplier
        scrollContainer.scrollLeft = scrollLeft - walk;
      });
      
      // Set initial cursor
      scrollContainer.style.cursor = 'grab';
      
      // Tag tab click handlers
      const tagTabs = document.querySelectorAll('.tag-tab');
      tagTabs.forEach(tab => {
        tab.addEventListener('click', (e) => {
          const tagId = e.currentTarget.dataset.tag;
          
          // Remove active class from all tabs
          tagTabs.forEach(t => {
            t.classList.remove('bg-blue-600', 'text-white', 'shadow-md');
            t.classList.add('bg-white', 'text-gray-700', 'border', 'border-gray-300');
          });
          
          // Add active class to clicked tab
          e.currentTarget.classList.remove('bg-white', 'text-gray-700', 'border', 'border-gray-300');
          e.currentTarget.classList.add('bg-blue-600', 'text-white', 'shadow-md');
          
          // Dispatch custom event for filtering
          window.dispatchEvent(new CustomEvent('tagChange', { 
            detail: { tagId } 
          }));
        });
      });
    }
  });
</script>
