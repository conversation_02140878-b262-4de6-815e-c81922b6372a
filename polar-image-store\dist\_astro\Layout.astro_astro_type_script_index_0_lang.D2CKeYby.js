document.addEventListener("DOMContentLoaded",()=>{const d=document.getElementById("mobile-menu-button"),u=document.getElementById("mobile-menu");d&&u&&d.addEventListener("click",()=>{u.classList.toggle("hidden")});const r=document.getElementById("headerSearchContainer"),a=document.getElementById("mobileHeaderSearchContainer");let c=!1;function m(){const e=window.scrollY;r&&(e<=50?(r.classList.add("opacity-0","-translate-y-2"),r.classList.remove("opacity-100","translate-y-0")):(r.classList.remove("opacity-0","-translate-y-2"),r.classList.add("opacity-100","translate-y-0"))),a&&(e<=50?(a.classList.add("opacity-0","-translate-y-2"),a.classList.remove("opacity-100","translate-y-0")):(a.classList.remove("opacity-0","-translate-y-2"),a.classList.add("opacity-100","translate-y-0"))),c=!1}function h(){c||(requestAnimationFrame(m),c=!0)}window.addEventListener("scroll",h,{passive:!0}),m();const i=document.getElementById("productSearch"),v=document.getElementById("mobileProductSearch"),t=document.getElementById("searchResults");let l;async function f(e){const s=e.value.trim();l&&clearTimeout(l),s.length>2?t&&(t.classList.remove("hidden"),t.innerHTML=`
            <div class="p-4 text-center text-primary-600">
              <div class="flex items-center justify-center gap-2">
                <svg class="animate-spin w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span class="text-sm">Searching tags for "${s}"...</span>
              </div>
            </div>
          `,l=setTimeout(async()=>{try{const n=await(await fetch(`/api/tags?q=${encodeURIComponent(s)}`)).json();if(t&&!t.classList.contains("hidden"))if(n.results&&n.results.length>0){const y=n.results.map(o=>`
                    <button onclick="window.location.href='${o.url}'" class="block w-full p-3 hover:bg-primary-50 rounded-lg transition-colors border-b border-primary-100 last:border-b-0 text-left">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                          <div class="w-8 h-8 bg-accent-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                            </svg>
                          </div>
                          <div class="flex-1 min-w-0">
                            <div class="text-primary-900 font-medium truncate">${o.displayName}</div>
                            <div class="text-primary-600 text-sm">${o.count} ${o.count===1?"product":"products"}</div>
                          </div>
                        </div>
                        <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </button>
                  `).join("");t.innerHTML=`
                    <div class="p-2">
                      <div class="text-xs text-primary-500 px-3 py-2 border-b border-primary-100">
                        Found ${n.total} tag${n.total===1?"":"s"}
                      </div>
                      ${y}
                      ${n.total>n.results.length?`
                        <div class="p-3 border-t border-primary-100">
                          <div class="text-center text-primary-600 text-sm">
                            Showing ${n.results.length} of ${n.total} tags
                          </div>
                        </div>
                      `:""}
                    </div>
                  `}else t.innerHTML=`
                    <div class="p-4 text-center">
                      <div class="text-primary-600 mb-2">No tags found for "${s}"</div>
                      <a href="/products" class="text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors">
                        Browse all products →
                      </a>
                    </div>
                  `}catch(p){console.error("Tag search error:",p),t&&!t.classList.contains("hidden")&&(t.innerHTML=`
                  <div class="p-4 text-center text-red-600">
                    <div class="text-sm">Search failed. Please try again.</div>
                  </div>
                `)}},300)):t&&t.classList.add("hidden")}i&&(i.addEventListener("input",e=>f(e.target)),i.addEventListener("keydown",e=>{if(e.key==="Enter"){e.preventDefault();const s=e.target.value.trim();s&&(window.location.href=`/products?search=${encodeURIComponent(s)}`)}})),v&&v.addEventListener("keydown",e=>{if(e.key==="Enter"){e.preventDefault();const s=e.target.value.trim();s&&(window.location.href=`/products?search=${encodeURIComponent(s)}`)}}),document.addEventListener("click",e=>{t&&!i?.contains(e.target)&&!t.contains(e.target)&&t.classList.add("hidden")})});
