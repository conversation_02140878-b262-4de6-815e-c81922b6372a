#!/usr/bin/env node

/**
 * Test script to verify Polar.sh API connection
 * Run with: node scripts/test-polar.js
 */

import { Polar } from '@polar-sh/sdk';
import { config } from 'dotenv';

// Load environment variables
config();

const accessToken = process.env.POLAR_ACCESS_TOKEN;
const organizationId = process.env.POLAR_ORGANIZATION_ID;

if (!accessToken) {
  console.error('❌ POLAR_ACCESS_TOKEN is required');
  console.log('Please set your Polar access token in .env file');
  process.exit(1);
}

if (!organizationId) {
  console.error('❌ POLAR_ORGANIZATION_ID is required');
  console.log('Please set your Polar organization ID in .env file');
  process.exit(1);
}

const polar = new Polar({
  accessToken,
  server: 'production'
});

async function testPolarConnection() {
  console.log('🔄 Testing Polar.sh API connection...\n');
  
  try {
    // Test 1: List organizations
    console.log('1. Testing organization access...');
    const orgs = await polar.organizations.list({});

    // Handle Polar API response structure: { result: { items: [...] } }
    const orgList = orgs.result?.items || [];
    console.log(`✅ Found ${orgList.length} organization(s)`);

    if (orgList.length > 0) {
      const targetOrg = orgList.find(org => org.id === organizationId);
      if (targetOrg) {
        console.log(`✅ Target organization found: ${targetOrg.name}`);
      } else {
        console.log(`⚠️  Organization ${organizationId} not found in accessible organizations`);
        console.log('Available organizations:');
        orgList.forEach(org => {
          console.log(`   - ${org.name} (${org.id})`);
        });
      }
    }
    
    // Test 2: List products
    console.log('\n2. Testing products access...');
    const products = await polar.products.list({
      organizationId,
      isArchived: false
    });

    const productList = products.result?.items || [];
    console.log(`✅ Found ${productList.length} product(s)`);

    if (productList.length > 0) {
      console.log('Products:');
      productList.forEach(product => {
        const price = product.prices[0]?.priceAmount ? `$${(product.prices[0].priceAmount / 100).toFixed(2)}` : 'Free';
        console.log(`   - ${product.name} (${price})`);
      });
    } else {
      console.log('⚠️  No products found. You may need to create some products in your Polar dashboard.');
    }
    
    // Test 3: Test checkout link creation (if products exist)
    if (productList.length > 0) {
      console.log('\n3. Testing checkout link creation...');
      const firstProduct = productList[0];
      
      try {
        const checkoutLink = await polar.checkoutLinks.create({
          paymentProcessor: 'stripe',
          productId: firstProduct.id,
          allowDiscountCodes: true,
          requireBillingAddress: false,
          successUrl: 'http://localhost:4321/success'
        });
        
        console.log(`✅ Checkout link created successfully`);
        console.log(`   URL: ${checkoutLink.url}`);
      } catch (error) {
        console.log(`❌ Failed to create checkout link: ${error.message}`);
      }
    }
    
    console.log('\n🎉 Polar.sh API connection test completed successfully!');
    console.log('\nNext steps:');
    console.log('1. If you don\'t have products, create some in your Polar dashboard');
    console.log('2. Update your .env file with the correct credentials');
    console.log('3. Run the development server: bun run dev');
    
  } catch (error) {
    console.error('\n❌ Polar.sh API connection failed:');
    console.error(`Error: ${error.message}`);
    
    if (error.message.includes('401')) {
      console.log('\n💡 This looks like an authentication error. Please check:');
      console.log('1. Your POLAR_ACCESS_TOKEN is correct');
      console.log('2. The token has the required permissions');
      console.log('3. You\'re using a production token (not sandbox)');
    } else if (error.message.includes('404')) {
      console.log('\n💡 This looks like a resource not found error. Please check:');
      console.log('1. Your POLAR_ORGANIZATION_ID is correct');
      console.log('2. The organization exists and you have access to it');
    }
    
    process.exit(1);
  }
}

// Run the test
testPolarConnection();
