# Polar.sh Setup Guide

Hướng dẫn chi tiết để setup Polar.sh cho dự án Polar Image Store.

## 🚀 Bước 1: Tạo tài khoản Polar.sh

1. T<PERSON>y cập [polar.sh](https://polar.sh)
2. Click "Sign up" và tạo tài khoản
3. <PERSON><PERSON><PERSON> thực email của bạn

## 🏢 Bước 2: Tạo Organization

1. <PERSON>u khi đăng nhập, click "Create Organization"
2. Nhập thông tin:
   - **Name**: Polar Image Store (hoặc tên bạn muốn)
   - **Slug**: polar-image-store
   - **Description**: Digital image marketplace
3. Click "Create Organization"

## 🔑 Bước 3: Lấy API Credentials

### Access Token
1. Vào **Settings** > **API**
2. Click "Create Personal Access Token"
3. Nhập tên: `Image Store API`
4. Chọn scopes cần thiết:
   - `products:read`
   - `products:write`
   - `checkout_links:read`
   - `checkout_links:write`
   - `webhooks:read`
   - `webhooks:write`
5. Click "Create Token"
6. **Copy token ngay** (chỉ hiển thị 1 lần)

### Organization ID
1. Vào trang Organization của bạn
2. Copy ID từ URL: `https://polar.sh/dashboard/org_XXXXXXXXXX`
3. Organization ID là phần `org_XXXXXXXXXX`

## 📦 Bước 4: Tạo Products

### Tạo sản phẩm mẫu:

1. Vào **Products** > **Create Product**
2. Điền thông tin:

**Product 1: Sunset Landscape**
- **Name**: Beautiful Sunset Landscape
- **Description**: Stunning sunset over mountains with vibrant colors. Perfect for websites, presentations, and print materials. High resolution 4K image.
- **Type**: One-time purchase
- **Price**: $9.99
- **Upload image**: Tải lên ảnh mẫu

**Product 2: Ocean Waves**
- **Name**: Ocean Waves Photography
- **Description**: Dramatic ocean waves crashing against rocks. Ideal for nature lovers and coastal themed projects. Professional quality image.
- **Type**: One-time purchase  
- **Price**: $12.99
- **Upload image**: Tải lên ảnh mẫu

**Product 3: City Skyline**
- **Name**: Modern City Skyline
- **Description**: Urban cityscape at night with illuminated buildings. Great for business presentations and modern design projects.
- **Type**: One-time purchase
- **Price**: $15.99
- **Upload image**: Tải lên ảnh mẫu

### Benefits Setup:
Cho mỗi product, thêm benefit:
1. **File Downloads**: Upload high-res image files
2. **License**: Commercial use license

## 🔗 Bước 5: Setup Webhooks

1. Vào **Settings** > **Webhooks**
2. Click "Create Webhook Endpoint"
3. Điền thông tin:
   - **URL**: `https://your-domain.com/api/webhooks` (sẽ cập nhật sau khi deploy)
   - **Events**: Chọn tất cả events liên quan đến products và orders
4. Click "Create"
5. **Copy webhook secret** để dùng trong .env

## ⚙️ Bước 6: Cập nhật Environment Variables

Cập nhật file `.env` với thông tin thực:

```env
# Polar.sh Configuration (PRODUCTION)
POLAR_ACCESS_TOKEN=polar_pat_your_real_token_here
POLAR_WEBHOOK_SECRET=whsec_your_real_webhook_secret_here
POLAR_ORGANIZATION_ID=org_your_real_organization_id_here

# Environment
NODE_ENV=development
PUBLIC_SITE_URL=http://localhost:4321
```

## 🧪 Bước 7: Test API Connection

Chạy script test để kiểm tra kết nối:

```bash
bun run test:polar
```

Script sẽ kiểm tra:
- ✅ Kết nối API
- ✅ Truy cập Organization
- ✅ Lấy danh sách Products
- ✅ Tạo Checkout Link

## 🚀 Bước 8: Test Website

1. Start development server:
```bash
bun run dev
```

2. Truy cập `http://localhost:4321`
3. Kiểm tra:
   - Trang chủ hiển thị đúng
   - Trang `/products` hiển thị sản phẩm từ Polar
   - Click "Buy Now" chuyển đến Polar checkout
   - Hoàn thành test purchase

## 🔧 Troubleshooting

### Lỗi 401 Unauthorized
- Kiểm tra `POLAR_ACCESS_TOKEN` đúng chưa
- Đảm bảo token có đủ permissions
- Kiểm tra token chưa hết hạn

### Lỗi 404 Not Found
- Kiểm tra `POLAR_ORGANIZATION_ID` đúng chưa
- Đảm bảo organization tồn tại và bạn có quyền truy cập

### Không có products
- Tạo ít nhất 1 product trong Polar dashboard
- Đảm bảo product không bị archived
- Kiểm tra organization ID đúng

### Webhook không hoạt động
- Cần deploy website lên production trước
- Cập nhật webhook URL trong Polar dashboard
- Kiểm tra webhook secret đúng

## 📝 Next Steps

Sau khi setup thành công:

1. **Customize products**: Thêm nhiều sản phẩm thực tế
2. **Upload real images**: Thay thế ảnh mẫu bằng ảnh thật
3. **Setup payment**: Cấu hình Stripe trong Polar
4. **Deploy production**: Deploy website lên Cloudflare
5. **Update webhook URL**: Cập nhật webhook URL production

## 💡 Tips

- **Test mode**: Polar có sandbox environment để test
- **Pricing**: Có thể tạo multiple price tiers cho 1 product
- **Discounts**: Có thể tạo discount codes
- **Analytics**: Polar cung cấp analytics dashboard
- **Customer portal**: Customers có thể manage subscriptions

## 🆘 Support

Nếu gặp vấn đề:
- [Polar Documentation](https://docs.polar.sh)
- [Polar Discord](https://discord.gg/polar)
- [GitHub Issues](https://github.com/polarsource/polar/issues)
