# Ngrok Setup for Webhook Testing

Hướng dẫn setup ngrok để test webhooks trong development.

## 📦 Cài đặt Ngrok

### Option 1: Download từ website
1. T<PERSON><PERSON> cập [ngrok.com](https://ngrok.com)
2. Tạo tài khoản miễn ph<PERSON>
3. Download ngrok cho Windows
4. G<PERSON><PERSON><PERSON> nén và thêm vào PATH

### Option 2: Sử dụng Chocolatey (Windows)
```bash
choco install ngrok
```

### Option 3: Sử dụng npm
```bash
npm install -g ngrok
```

## 🔑 Setup Authentication

1. Lấy authtoken từ [ngrok dashboard](https://dashboard.ngrok.com/get-started/your-authtoken)
2. <PERSON><PERSON><PERSON> hình authtoken:
```bash
ngrok authtoken YOUR_AUTHTOKEN_HERE
```

## 🚀 Sử dụng Ngrok

### Bước 1: Start Development Server
```bash
bun run dev
```
Website sẽ chạy tại `http://localhost:4321`

### Bước 2: Start Ngrok Tunnel
Mở terminal mới và chạy:
```bash
ngrok http 4321
```

Ngrok sẽ tạo public URL như:
```
https://abc123.ngrok.io -> http://localhost:4321
```

### Bước 3: Cập nhật Webhook URL
1. Copy HTTPS URL từ ngrok (ví dụ: `https://abc123.ngrok.io`)
2. Vào Polar dashboard > Settings > Webhooks
3. Cập nhật webhook URL thành: `https://abc123.ngrok.io/api/webhooks`
4. Save changes

## 🧪 Test Webhooks

### Test Product Events
1. Tạo/sửa/xóa product trong Polar dashboard
2. Kiểm tra console của dev server
3. Sẽ thấy webhook events được log

### Test Order Events
1. Thực hiện test purchase trên website
2. Complete payment trong Polar checkout
3. Kiểm tra webhook events trong console

## 📝 Ngrok Commands

```bash
# Start tunnel
ngrok http 4321

# Start tunnel với custom subdomain (paid plan)
ngrok http 4321 --subdomain=polar-image-store

# Start tunnel với basic auth
ngrok http 4321 --basic-auth="username:password"

# View web interface
# Truy cập http://localhost:4040 để xem requests
```

## 🔧 Troubleshooting

### Ngrok không kết nối được
- Kiểm tra authtoken đã setup chưa
- Đảm bảo port 4321 đang được sử dụng bởi Astro
- Kiểm tra firewall settings

### Webhook không nhận được
- Kiểm tra ngrok URL đã cập nhật trong Polar chưa
- Đảm bảo URL có `/api/webhooks` ở cuối
- Kiểm tra webhook secret đúng chưa

### SSL Certificate errors
- Ngrok tự động cung cấp HTTPS
- Nếu có lỗi SSL, thử restart ngrok

## 💡 Tips

- **Keep ngrok running**: Đừng tắt ngrok khi đang test
- **URL changes**: Mỗi lần restart ngrok, URL sẽ thay đổi (free plan)
- **Paid plan**: Có fixed subdomain, không đổi URL
- **Web interface**: `http://localhost:4040` để xem all requests
- **Logs**: Ngrok logs all HTTP requests, rất hữu ích để debug

## 🔄 Development Workflow

1. Start Astro dev server: `bun run dev`
2. Start ngrok: `ngrok http 4321`
3. Update webhook URL in Polar dashboard
4. Test webhooks by creating/updating products
5. Check console logs for webhook events
6. Develop webhook handlers based on events received

## 🚀 Production Deployment

Khi deploy production:
1. Deploy website lên Cloudflare Pages
2. Cập nhật webhook URL thành production URL
3. Không cần ngrok nữa