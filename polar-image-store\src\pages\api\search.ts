import type { APIRoute } from 'astro';
import { createPolarClient, transformPolarProduct, extractUniqueTags, getTagDisplayName } from '../../utils/polar';
import type { LocalProduct } from '../../types/polar';

export const prerender = false;

export const GET: APIRoute = async ({ url }) => {
  try {
    const query = url.searchParams.get('q');
    
    const polar = createPolarClient();
    const organizationId = import.meta.env.POLAR_ORGANIZATION_ID;

    if (!organizationId) {
      return new Response(
        JSON.stringify({ error: 'Organization ID not configured' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get products from Polar
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });

    const productList = response.result?.items || [];
    const products = productList
      .map(transformPolarProduct)
      .filter((product): product is LocalProduct => product !== null);

    // Extract all unique tags
    const allTags = extractUniqueTags(products);

    // If no query, return popular tags
    if (!query || query.trim().length < 2) {
      const popularTags = allTags
        .map(tag => {
          const tagProducts = products.filter(product =>
            product.tags && product.tags.includes(tag)
          );
          return {
            id: tag,
            name: getTagDisplayName(tag),
            displayName: getTagDisplayName(tag),
            count: tagProducts.length,
            url: `/products/tag/${encodeURIComponent(tag)}`
          };
        })
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      return new Response(
        JSON.stringify({
          results: [],
          popularTags
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'public, max-age=300' // Cache for 5 minutes
          }
        }
      );
    }

    // Search for tags
    const searchTerm = query.toLowerCase().trim();

    // Filter tags based on search query
    const matchingTags = allTags.filter(tag => {
      const tagName = getTagDisplayName(tag).toLowerCase();
      const tagId = tag.toLowerCase();

      // Match against both tag ID and display name
      return tagId.includes(searchTerm) || tagName.includes(searchTerm);
    });

    // Create tag results with product counts
    const tagResults = matchingTags.map(tag => {
      const tagProducts = products.filter(product =>
        product.tags && product.tags.includes(tag)
      );

      return {
        id: tag,
        name: getTagDisplayName(tag),
        displayName: getTagDisplayName(tag),
        count: tagProducts.length,
        url: `/products/tag/${encodeURIComponent(tag)}`
      };
    });

    // Sort by relevance (same as search page)
    tagResults.sort((a, b) => {
      const aExactMatch = a.name.toLowerCase() === searchTerm || a.id.toLowerCase() === searchTerm;
      const bExactMatch = b.name.toLowerCase() === searchTerm || b.id.toLowerCase() === searchTerm;

      if (aExactMatch && !bExactMatch) return -1;
      if (!aExactMatch && bExactMatch) return 1;

      // If both or neither are exact matches, sort by product count (descending)
      if (b.count !== a.count) {
        return b.count - a.count;
      }

      // Finally, sort alphabetically
      return a.name.localeCompare(b.name);
    });

    return new Response(
      JSON.stringify({
        results: tagResults,
        total: tagResults.length,
        query: query
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=60' // Cache for 1 minute
        }
      }
    );

  } catch (error) {
    console.error('Search API error:', error);
    return new Response(
      JSON.stringify({ error: 'Search failed' }),
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    );
  }
};
