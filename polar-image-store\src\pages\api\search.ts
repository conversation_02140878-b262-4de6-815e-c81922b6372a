import type { APIRoute } from 'astro';
import { createPolarClient, transformPolarProduct } from '../../utils/polar';
import type { LocalProduct } from '../../types/polar';

export const prerender = false;

export const GET: APIRoute = async ({ url }) => {
  try {
    const query = url.searchParams.get('q');
    
    if (!query || query.trim().length < 2) {
      return new Response(
        JSON.stringify({ results: [] }),
        { 
          status: 200, 
          headers: { 
            'Content-Type': 'application/json',
            'Cache-Control': 'public, max-age=60' // Cache for 1 minute
          } 
        }
      );
    }

    const polar = createPolarClient();
    const organizationId = import.meta.env.POLAR_ORGANIZATION_ID;
    
    if (!organizationId) {
      return new Response(
        JSON.stringify({ error: 'Organization ID not configured' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get products from Polar
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });

    const productList = response.result?.items || [];
    const allProducts = productList
      .map(transformPolarProduct)
      .filter((product): product is LocalProduct => product !== null);

    // Filter products based on search query
    const searchQuery = query.toLowerCase().trim();
    const filteredProducts = allProducts.filter((product) => {
      return (
        product.name.toLowerCase().includes(searchQuery) ||
        product.description.toLowerCase().includes(searchQuery) ||
        (product.tags && product.tags.some(tag => tag.toLowerCase().includes(searchQuery)))
      );
    });

    // Limit to top 5 results for suggestions
    const results = filteredProducts.slice(0, 5).map(product => ({
      id: product.id,
      name: product.name,
      slug: product.slug,
      price: product.price,
      currency: product.currency,
      image: product.images[0] || null,
      description: product.description.substring(0, 100) + (product.description.length > 100 ? '...' : '')
    }));

    return new Response(
      JSON.stringify({ 
        results,
        total: filteredProducts.length,
        query: query
      }),
      { 
        status: 200, 
        headers: { 
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=60' // Cache for 1 minute
        } 
      }
    );

  } catch (error) {
    console.error('Search API error:', error);
    return new Response(
      JSON.stringify({ error: 'Search failed' }),
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    );
  }
};
