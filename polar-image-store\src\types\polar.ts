// Polar.sh types for our application
export interface PolarProduct {
  id: string;
  name: string;
  description: string;
  isRecurring: boolean;
  isArchived: boolean;
  organizationId: string;
  createdAt: string;
  modifiedAt: string;
  prices: PolarPrice[];
  benefits: PolarBenefit[];
  medias: PolarMedia[];
  metadata?: Record<string, any>;
}

export interface PolarPrice {
  id: string;
  productId: string;
  type: 'one_time' | 'recurring';
  amountType: 'fixed' | 'custom' | 'free';
  priceAmount?: number;
  priceCurrency?: string;
  recurringInterval?: 'month' | 'year';
  isArchived: boolean;
  createdAt: string;
  modifiedAt: string;
}

export interface PolarBenefit {
  id: string;
  type: 'file_downloads' | 'license_keys' | 'github_access' | 'discord_access' | 'custom';
  description: string;
  selectable: boolean;
  deletable: boolean;
  organizationId: string;
  properties?: Record<string, any>;
}

export interface PolarMedia {
  id: string;
  organizationId: string;
  name: string;
  path: string;
  mimeType: string;
  size: number;
  publicUrl: string;
  isUploaded: boolean;
  createdAt: string;
}

export interface PolarCheckoutLink {
  id: string;
  url: string;
  productId?: string;
  products?: string[];
  paymentProcessor: 'stripe';
  allowDiscountCodes: boolean;
  requireBillingAddress: boolean;
  successUrl?: string;
  metadata?: Record<string, any>;
}

export interface PolarWebhookPayload {
  type: string;
  data: any;
}

// Local product interface for our website
export interface LocalProduct {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  images: string[];
  slug: string;
  isAvailable: boolean;
  checkoutUrl?: string;
  tags?: string[];
  category?: string;
  createdAt: string;
  updatedAt: string;
}
