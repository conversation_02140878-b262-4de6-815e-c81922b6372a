---
// SearchModal component - Mobile slide-up modal for search functionality
// Preserves all features from /search page
---

<!-- Search Modal - Hidden by default -->
<div 
  id="searchModal" 
  class="fixed inset-0 z-[9999] hidden"
  role="dialog"
  aria-modal="true"
  aria-labelledby="searchModalTitle"
>
  <!-- Backdrop -->
  <div 
    id="searchModalBackdrop"
    class="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300 opacity-0"
    aria-hidden="true"
  ></div>
  
  <!-- Modal Content -->
  <div 
    id="searchModalContent"
    class="fixed inset-x-0 bottom-0 bg-white rounded-t-2xl shadow-2xl transform translate-y-full transition-transform duration-300 ease-out max-h-[90vh] flex flex-col"
  >
    <!-- Header with back button and search input -->
    <div class="sticky top-0 z-50 bg-white border-b border-gray-200 rounded-t-2xl">
      <div class="flex items-center px-4 py-3">
        <button 
          id="searchModalBackButton"
          class="mr-3 p-2 -ml-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors"
          aria-label="Close search"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        
        <!-- Search Input -->
        <div class="flex-1 relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            type="text"
            id="searchModalInput"
            placeholder="Search for images..."
            class="block w-full pl-10 pr-4 py-3 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200 text-lg"
            autocomplete="off"
          />
          
          <!-- Clear button -->
          <button
            id="searchModalClearButton"
            class="absolute inset-y-0 right-0 pr-3 flex items-center text-primary-400 hover:text-primary-600 transition-colors duration-200"
            style="display: none;"
            aria-label="Clear search"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Scrollable Content -->
    <div class="flex-1 overflow-y-auto px-4 py-6">
      <!-- Search Results Container -->
      <div id="searchModalResults" class="hidden">
        <div id="searchModalResultsHeader" class="mb-6">
          <p id="searchModalResultsText" class="text-gray-600"></p>
        </div>

        <!-- Error State -->
        <div id="searchModalError" class="hidden bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden">
          <div class="p-4 text-center text-red-600">
            <div id="searchModalErrorText" class="text-sm"></div>
          </div>
        </div>

        <!-- Results List -->
        <div id="searchModalResultsList" class="hidden bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden">
          <!-- Header Section -->
          <div id="searchModalResultsListHeader" class="text-xs text-primary-500 px-3 py-2 border-b border-primary-100 bg-gray-50">
          </div>

          <!-- Results Items -->
          <div id="searchModalResultsItems" class="p-2">
            <!-- Results will be populated here -->
          </div>

          <!-- Footer Section -->
          <div id="searchModalResultsFooter" class="hidden p-3 border-t border-primary-100 bg-gray-50">
            <div class="text-center text-primary-600 text-sm">
              <span id="searchModalResultsCount"></span>
            </div>
          </div>
        </div>

        <!-- No Results -->
        <div id="searchModalNoResults" class="hidden bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden">
          <div class="p-4 text-center">
            <div id="searchModalNoResultsText" class="text-primary-600 mb-2"></div>
            <a href="/products" class="text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors">
              Browse all products →
            </a>
          </div>

          <!-- Popular Search Suggestions -->
          <div id="searchModalSuggestions" class="hidden border-t border-primary-100 p-4">
            <p class="text-sm font-medium text-primary-900 mb-3 text-center">Try these searches:</p>
            <div id="searchModalSuggestionsContainer" class="flex flex-wrap gap-2 justify-center">
              <!-- Suggestions will be populated here -->
            </div>
          </div>
        </div>
      </div>

      <!-- Initial Search State -->
      <div id="searchModalInitialState">
        <!-- Recent Searches -->
        <div id="searchModalRecentSearches" class="mb-8" style="display: none;">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold text-primary-900">Recent Searches</h2>
            <button
              id="searchModalClearRecentSearches"
              class="p-2 text-primary-400 hover:text-primary-600 hover:bg-primary-50 rounded-full transition-all duration-200"
              title="Clear recent searches"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
          <div id="searchModalRecentSearchesList" class="bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden">
            <!-- Recent searches will be populated here -->
          </div>
        </div>

        <!-- Popular Tags -->
        <div id="searchModalPopularTags" class="mb-8">
          <h2 class="text-lg font-semibold text-primary-900 mb-4">Popular Searches</h2>
          <div id="searchModalPopularTagsList" class="bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden">
            <div class="p-2" id="searchModalPopularTagsContainer">
              <!-- Popular tags will be populated here -->
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div>
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Browse</h2>
          <div class="space-y-3">
            <a
              href="/products"
              class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <span class="font-medium text-gray-900">All Products</span>
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  /* Ensure modal is above everything */
  #searchModal {
    z-index: 9999;
  }
  
  /* Smooth animations */
  #searchModalContent {
    will-change: transform;
  }
  
  #searchModalBackdrop {
    will-change: opacity;
  }
  
  /* Hide scrollbar but keep functionality */
  #searchModal .overflow-y-auto::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }
  
  /* Line clamp utility */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>

<script>
  // Search Modal functionality
  class SearchModal {
    private modal: HTMLElement | null = null;
    private backdrop: HTMLElement | null = null;
    private content: HTMLElement | null = null;
    private input: HTMLInputElement | null = null;
    private clearButton: HTMLElement | null = null;
    private backButton: HTMLElement | null = null;
    private searchTimeout: any = null;
    private isOpen: boolean = false;

    constructor() {
      this.init();
    }

    private init() {
      // Get DOM elements
      this.modal = document.getElementById('searchModal');
      this.backdrop = document.getElementById('searchModalBackdrop');
      this.content = document.getElementById('searchModalContent');
      this.input = document.getElementById('searchModalInput') as HTMLInputElement;
      this.clearButton = document.getElementById('searchModalClearButton');
      this.backButton = document.getElementById('searchModalBackButton');

      if (!this.modal || !this.backdrop || !this.content || !this.input) {
        console.error('SearchModal: Required elements not found');
        return;
      }

      this.setupEventListeners();
      this.loadPopularTags();
    }

    private setupEventListeners() {
      // Back button and backdrop close modal
      this.backButton?.addEventListener('click', () => this.close());
      this.backdrop?.addEventListener('click', () => this.close());

      // Clear button functionality
      this.clearButton?.addEventListener('click', () => {
        this.input!.value = '';
        this.toggleClearButton();
        this.showInitialState();
        this.syncUrlToCleanState();
        this.input!.focus();
      });

      // Search input functionality
      this.input?.addEventListener('input', () => {
        this.toggleClearButton();

        // Clear previous timeout
        if (this.searchTimeout) {
          clearTimeout(this.searchTimeout);
        }

        const query = this.input!.value.trim();
        if (query === '') {
          this.showInitialState();
          this.syncUrlToCleanState();
        } else if (query.length >= 2) {
          // Debounced search
          this.searchTimeout = setTimeout(() => {
            this.performSearch(query);
          }, 500);
        }
      });

      // Keyboard navigation
      this.input?.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          const query = this.input!.value.trim();
          if (query) {
            this.performSearch(query);
          }
        } else if (e.key === 'Escape') {
          e.preventDefault();
          this.close();
        }
      });

      // Prevent modal close when clicking inside content
      this.content?.addEventListener('click', (e) => {
        e.stopPropagation();
      });

      // Recent searches functionality
      const clearRecentButton = document.getElementById('searchModalClearRecentSearches');
      clearRecentButton?.addEventListener('click', () => {
        localStorage.removeItem('recentSearches');
        this.hideRecentSearches();
      });
    }

    public open(initialQuery: string = '') {
      if (!this.modal || this.isOpen) return;

      this.isOpen = true;
      this.modal.classList.remove('hidden');

      // Set initial query if provided
      if (initialQuery && this.input) {
        this.input.value = initialQuery;
        this.toggleClearButton();
        if (initialQuery.length >= 2) {
          this.performSearch(initialQuery);
        }
      } else {
        this.showInitialState();
        this.loadRecentSearches();
      }

      // Trigger animations
      requestAnimationFrame(() => {
        this.backdrop?.classList.add('opacity-100');
        this.backdrop?.classList.remove('opacity-0');
        this.content?.classList.add('translate-y-0');
        this.content?.classList.remove('translate-y-full');
      });

      // Focus input after animation
      setTimeout(() => {
        this.input?.focus();
      }, 300);

      // Prevent body scroll
      document.body.style.overflow = 'hidden';
    }

    public close() {
      if (!this.modal || !this.isOpen) return;

      this.isOpen = false;

      // Trigger close animations
      this.backdrop?.classList.add('opacity-0');
      this.backdrop?.classList.remove('opacity-100');
      this.content?.classList.add('translate-y-full');
      this.content?.classList.remove('translate-y-0');

      // Hide modal after animation
      setTimeout(() => {
        this.modal?.classList.add('hidden');
        // Reset state
        if (this.input) {
          this.input.value = '';
        }
        this.toggleClearButton();
        this.showInitialState();
        this.syncUrlToCleanState();
      }, 300);

      // Restore body scroll
      document.body.style.overflow = '';
    }

    private toggleClearButton() {
      if (!this.clearButton || !this.input) return;
      this.clearButton.style.display = this.input.value.trim() ? 'flex' : 'none';
    }

    private showInitialState() {
      const resultsContainer = document.getElementById('searchModalResults');
      const initialState = document.getElementById('searchModalInitialState');

      resultsContainer?.classList.add('hidden');
      initialState?.classList.remove('hidden');
    }

    private showResults() {
      const resultsContainer = document.getElementById('searchModalResults');
      const initialState = document.getElementById('searchModalInitialState');

      resultsContainer?.classList.remove('hidden');
      initialState?.classList.add('hidden');
    }

    private syncUrlToCleanState() {
      if (window.location.search) {
        window.history.replaceState({}, '', window.location.pathname);
      }
    }

    private async performSearch(query: string) {
      this.saveRecentSearch(query);
      this.showResults();

      // Update URL
      const newUrl = `${window.location.pathname}?q=${encodeURIComponent(query)}`;
      window.history.replaceState({ query }, '', newUrl);

      try {
        const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
        const data = await response.json();

        if (data.error) {
          this.showError(data.error);
        } else {
          this.displayResults(query, data.results || []);
        }
      } catch (error) {
        console.error('Search error:', error);
        this.showError('Failed to search tags');
      }
    }

    private displayResults(query: string, results: any[]) {
      const resultsText = document.getElementById('searchModalResultsText');
      const errorContainer = document.getElementById('searchModalError');
      const resultsList = document.getElementById('searchModalResultsList');
      const noResults = document.getElementById('searchModalNoResults');

      // Hide error
      errorContainer?.classList.add('hidden');

      // Update results text
      if (resultsText) {
        resultsText.textContent = results.length > 0
          ? `Found ${results.length} tag${results.length === 1 ? '' : 's'} for "${query}"`
          : `No results for "${query}"`;
      }

      if (results.length > 0) {
        this.showResultsList(query, results);
        noResults?.classList.add('hidden');
      } else {
        this.showNoResults(query);
        resultsList?.classList.add('hidden');
      }
    }

    private showResultsList(query: string, results: any[]) {
      const resultsList = document.getElementById('searchModalResultsList');
      const resultsHeader = document.getElementById('searchModalResultsListHeader');
      const resultsItems = document.getElementById('searchModalResultsItems');
      const resultsFooter = document.getElementById('searchModalResultsFooter');
      const resultsCount = document.getElementById('searchModalResultsCount');

      if (!resultsList || !resultsItems) return;

      resultsList.classList.remove('hidden');

      // Update header
      if (resultsHeader) {
        resultsHeader.textContent = `Search All images for "${query}"`;
      }

      // Update items
      resultsItems.innerHTML = results.map((result, index) => `
        <a
          href="${result.url}"
          class="block w-full p-3 hover:bg-primary-50 rounded-lg transition-colors text-left ${
            index < results.length - 1 ? 'border-b border-primary-100' : ''
          }"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-accent-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
              </div>
              <div class="flex-1 min-w-0">
                <div class="text-primary-900 font-medium truncate">${result.displayName}</div>
                <div class="text-primary-600 text-sm">${result.count} ${result.count === 1 ? 'product' : 'products'}</div>
              </div>
            </div>
            <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </a>
      `).join('');

      // Update footer
      if (results.length >= 8 && resultsFooter && resultsCount) {
        resultsFooter.classList.remove('hidden');
        resultsCount.textContent = `Showing ${results.length} results`;
      } else {
        resultsFooter?.classList.add('hidden');
      }
    }

    private showNoResults(query: string) {
      const noResults = document.getElementById('searchModalNoResults');
      const noResultsText = document.getElementById('searchModalNoResultsText');
      const suggestions = document.getElementById('searchModalSuggestions');
      const suggestionsContainer = document.getElementById('searchModalSuggestionsContainer');

      if (!noResults) return;

      noResults.classList.remove('hidden');

      // Update no results text
      if (noResultsText) {
        noResultsText.textContent = `No results for "${query}"`;
      }

      // Show popular suggestions
      if (suggestions && suggestionsContainer) {
        const popularTags = this.getPopularTags();
        if (popularTags.length > 0) {
          suggestions.classList.remove('hidden');
          suggestionsContainer.innerHTML = popularTags.slice(0, 6).map(tag => `
            <button
              class="tag-suggestion px-3 py-1 bg-primary-50 text-primary-700 rounded-full text-sm hover:bg-primary-100 transition-colors border border-primary-200"
              data-tag="${tag.id}"
            >
              #${tag.name}
            </button>
          `).join('');

          // Add click listeners to suggestions
          suggestionsContainer.querySelectorAll('.tag-suggestion').forEach(button => {
            button.addEventListener('click', () => {
              const tag = button.getAttribute('data-tag');
              if (tag && this.input) {
                this.input.value = tag;
                this.performSearch(tag);
              }
            });
          });
        } else {
          suggestions.classList.add('hidden');
        }
      }
    }

    private showError(error: string) {
      const errorContainer = document.getElementById('searchModalError');
      const errorText = document.getElementById('searchModalErrorText');
      const resultsList = document.getElementById('searchModalResultsList');
      const noResults = document.getElementById('searchModalNoResults');

      if (!errorContainer || !errorText) return;

      errorContainer.classList.remove('hidden');
      errorText.textContent = `${error}. Please try again.`;

      resultsList?.classList.add('hidden');
      noResults?.classList.add('hidden');
    }

    private saveRecentSearch(query: string) {
      try {
        const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
        const filtered = recent.filter((item: string) => item !== query);
        filtered.unshift(query);
        const limited = filtered.slice(0, 10);
        localStorage.setItem('recentSearches', JSON.stringify(limited));
      } catch (error) {
        console.error('Failed to save recent search:', error);
      }
    }

    private loadRecentSearches() {
      try {
        const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
        const recentContainer = document.getElementById('searchModalRecentSearches');
        const recentList = document.getElementById('searchModalRecentSearchesList');

        if (recent.length > 0 && recentContainer && recentList) {
          recentContainer.style.display = 'block';
          recentList.innerHTML = `
            <div class="p-2">
              ${recent.map((query: string, index: number) => `
                <button class="recent-search-item block w-full p-3 hover:bg-primary-50 rounded-lg transition-colors text-left ${
                  index < recent.length - 1 ? 'border-b border-primary-100' : ''
                }" data-query="${query}">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div class="flex-1 min-w-0">
                        <div class="text-primary-900 font-medium truncate">${query}</div>
                        <div class="text-primary-600 text-sm">Recent search</div>
                      </div>
                    </div>
                    <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </button>
              `).join('')}
            </div>
          `;

          // Add click listeners
          recentList.querySelectorAll('.recent-search-item').forEach(item => {
            item.addEventListener('click', () => {
              const query = item.getAttribute('data-query');
              if (query && this.input) {
                this.input.value = query;
                this.performSearch(query);
              }
            });
          });
        }
      } catch (error) {
        console.error('Failed to load recent searches:', error);
      }
    }

    private hideRecentSearches() {
      const recentContainer = document.getElementById('searchModalRecentSearches');
      if (recentContainer) {
        recentContainer.style.display = 'none';
      }
    }

    private async loadPopularTags() {
      try {
        const response = await fetch('/api/search?q=');
        const data = await response.json();

        if (data.popularTags && data.popularTags.length > 0) {
          this.popularTagsCache = data.popularTags;
          this.displayPopularTags(data.popularTags);
        }
      } catch (error) {
        console.error('Failed to load popular tags:', error);
      }
    }

    private displayPopularTags(tags: any[]) {
      const container = document.getElementById('searchModalPopularTagsContainer');
      if (!container) return;

      container.innerHTML = tags.map((tag, index) => `
        <button
          class="tag-suggestion block w-full p-3 hover:bg-primary-50 rounded-lg transition-colors text-left ${
            index < tags.length - 1 ? 'border-b border-primary-100' : ''
          }"
          data-tag="${tag.id}"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-accent-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
              </div>
              <div class="flex-1 min-w-0">
                <div class="text-primary-900 font-medium truncate">#${tag.name}</div>
                <div class="text-primary-600 text-sm">${tag.count} ${tag.count === 1 ? 'product' : 'products'}</div>
              </div>
            </div>
            <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </button>
      `).join('');

      // Add click listeners
      container.querySelectorAll('.tag-suggestion').forEach(button => {
        button.addEventListener('click', () => {
          const tag = button.getAttribute('data-tag');
          if (tag && this.input) {
            this.input.value = tag;
            this.performSearch(tag);
          }
        });
      });
    }

    private popularTagsCache: any[] = [];

    private getPopularTags(): any[] {
      return this.popularTagsCache;
    }
  }

  // Initialize search modal when DOM is loaded
  let searchModalInstance: SearchModal | null = null;

  document.addEventListener('DOMContentLoaded', () => {
    searchModalInstance = new SearchModal();
  });

  // Export function to open modal (will be called from Layout.astro)
  (window as any).openSearchModal = (initialQuery: string = '') => {
    searchModalInstance?.open(initialQuery);
  };

  // Export function to close modal
  (window as any).closeSearchModal = () => {
    searchModalInstance?.close();
  };
</script>
