import { Webhooks } from '@polar-sh/astro';
import type { PolarWebhookPayload } from '../../types/polar';

export const prerender = false;

export const POST = Webhooks({
  webhookSecret: import.meta.env.POLAR_WEBHOOK_SECRET,
  onPayload: async (payload: PolarWebhookPayload) => {
    console.log('Received webhook:', payload.type);
    
    try {
      switch (payload.type) {
        case 'product.created':
          await handleProductCreated(payload.data);
          break;
        case 'product.updated':
          await handleProductUpdated(payload.data);
          break;
        case 'product.deleted':
          await handleProductDeleted(payload.data);
          break;
        case 'order.paid':
          await handleOrderPaid(payload.data);
          break;
        default:
          console.log('Unhandled webhook type:', payload.type);
      }
    } catch (error) {
      console.error('Error processing webhook:', error);
      throw error;
    }
  }
});

async function handleProductCreated(product: any) {
  console.log('Product created:', product.id, product.name);
  // TODO: Save product to local storage/database
}

async function handleProductUpdated(product: any) {
  console.log('Product updated:', product.id, product.name);
  // TODO: Update product in local storage/database
}

async function handleProductDeleted(product: any) {
  console.log('Product deleted:', product.id);
  // TODO: Remove product from local storage/database
}

async function handleOrderPaid(order: any) {
  console.log('Order paid:', order.id);
  // TODO: Handle successful payment (analytics, notifications, etc.)
}
