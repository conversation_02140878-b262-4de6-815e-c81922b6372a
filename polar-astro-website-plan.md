# Dự án Website Hiển thị Sản phẩm Ảnh từ Polar.sh với Astro

## Tổng quan dự án

Tạo một website để hiển thị các sản phẩm ảnh từ Polar.sh sử dụng Astro.build và deploy lên Cloudflare Pages. Website sẽ đồng bộ sản phẩm từ Polar.sh và cho phép người dùng mua hàng thông qua checkout link.

## 1. Nghiên cứu về Polar.sh

### Thông tin cơ bản
- **Polar.sh** là một nền tảng Merchant of Record mã nguồn mở
- Phí giao dịch: 4% + 40¢ (thấp hơn 20% so với các đối thủ)
- Hỗ trợ thanh toán quốc tế với xử lý thuế tự động
- API mạnh mẽ với SDK cho nhiều ngôn ngữ

### Tính năng chính
- **Products**: Quản lý sản phẩm số (one-time, subscription)
- **Checkout**: Tạo checkout link và embedded checkout
- **Webhooks**: Đồng bộ dữ liệu real-time
- **Benefits**: Tự động hóa quyền lợi (file downloads, license keys)
- **API**: RESTful API với rate limit 100 requests/second

### API quan trọng cho dự án
```typescript
// Lấy danh sách sản phẩm
const products = await polar.products.list({
  organizationId: "org_id"
});

// Tạo checkout link
const checkoutLink = await polar.checkoutLinks.create({
  paymentProcessor: "stripe",
  productId: "product_id"
});

// Webhook events
- product.created
- product.updated
- checkout.created
- order.paid
```

## 2. Nghiên cứu về Astro.build

### Thông tin cơ bản
- **Astro** là JavaScript web framework tối ưu cho content-driven websites
- Server-first rendering với zero JavaScript overhead
- Hỗ trợ multiple UI frameworks (React, Vue, Svelte)
- Performance tốt nhất trong các framework (60% sites có good Core Web Vitals)

### Tính năng phù hợp với dự án
- **Static Site Generation**: Tối ưu cho hiển thị sản phẩm
- **Content Collections**: Quản lý dữ liệu sản phẩm
- **API Routes**: Xử lý webhooks và API calls
- **Image Optimization**: Tối ưu hình ảnh sản phẩm
- **View Transitions**: UX mượt mà

### Astro Polar Integration
```bash
pnpm install @polar-sh/astro zod
```

```typescript
// Checkout handler
import { Checkout } from "@polar-sh/astro";

export const GET = Checkout({
  accessToken: POLAR_ACCESS_TOKEN,
  successUrl: POLAR_SUCCESS_URL,
  server: "production"
});

// Webhook handler
import { Webhooks } from '@polar-sh/astro';

export const POST = Webhooks({
  webhookSecret: POLAR_WEBHOOK_SECRET,
  onPayload: async (payload) => {
    // Xử lý webhook events
  }
});
```

## 3. Deploy Astro lên Cloudflare

### Cloudflare Pages vs Workers
- **Cloudflare Pages**: Phù hợp cho static sites
- **Cloudflare Workers**: Hỗ trợ full-stack apps với APIs

### Cách deploy với Wrangler
```bash
# Cài đặt Wrangler
npm install wrangler@latest --save-dev

# Cấu hình wrangler.jsonc
{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "polar-image-store",
  "compatibility_date": "2025-03-25",
  "assets": {
    "directory": "./dist",
    "not_found_handling": "404-page"
  }
}

# Build và deploy
npx astro build && npx wrangler pages deploy ./dist
```

### Deploy với Git Integration
1. Push code lên GitHub/GitLab
2. Connect repository trong Cloudflare Dashboard
3. Cấu hình build:
   - Framework preset: `Astro`
   - Build command: `npm run build`
   - Build output directory: `dist`

## 4. Kiến trúc Website

### Không cần tính năng login
- Website hoạt động như catalog sản phẩm
- Người dùng browse và click "Mua ngay"
- Chuyển hướng đến Polar checkout page
- Sau thanh toán thành công, Polar xử lý delivery

### Flow mua hàng
1. User browse sản phẩm trên website
2. Click "Mua ngay" → redirect đến Polar checkout
3. Thanh toán trên Polar
4. Polar gửi webhook về website
5. Website cập nhật trạng thái (nếu cần)

## 5. Đồng bộ sản phẩm với Polar.sh

### Sử dụng Webhooks
```typescript
// Webhook events cần xử lý
const webhookHandlers = {
  'product.created': handleProductCreated,
  'product.updated': handleProductUpdated,
  'product.deleted': handleProductDeleted,
  'order.paid': handleOrderPaid
};

async function handleProductCreated(payload) {
  // Lưu sản phẩm mới vào database/file
  const product = payload.data;
  await saveProduct({
    id: product.id,
    name: product.name,
    description: product.description,
    price: product.prices[0]?.priceAmount,
    images: product.medias.map(m => m.publicUrl)
  });
}
```

### Sử dụng API để sync ban đầu
```typescript
// Lấy tất cả sản phẩm từ Polar
async function syncProducts() {
  const products = await polar.products.list({
    organizationId: "your_org_id",
    isArchived: false
  });
  
  for (const product of products.result) {
    await saveProduct(product);
  }
}
```

## 6. Yêu cầu kỹ thuật

### Environment Variables
```bash
POLAR_ACCESS_TOKEN=polar_pat_xxx
POLAR_WEBHOOK_SECRET=whsec_xxx
POLAR_ORGANIZATION_ID=org_xxx
```

### Dependencies chính
```json
{
  "@polar-sh/astro": "latest",
  "@polar-sh/sdk": "latest",
  "astro": "^5.0.0",
  "zod": "latest"
}
```

### Database/Storage
- **Option 1**: JSON files trong repo (simple)
- **Option 2**: Cloudflare D1 (SQL database)
- **Option 3**: External database (PostgreSQL, MongoDB)

## 7. Cấu trúc thư mục đề xuất

```
src/
├── pages/
│   ├── index.astro              # Trang chủ
│   ├── products/
│   │   ├── index.astro          # Danh sách sản phẩm
│   │   └── [slug].astro         # Chi tiết sản phẩm
│   ├── api/
│   │   ├── checkout.ts          # Checkout handler
│   │   └── webhooks.ts          # Webhook handler
├── components/
│   ├── ProductCard.astro
│   ├── ProductGallery.astro
│   └── BuyButton.astro
├── layouts/
│   └── Layout.astro
└── content/
    └── products/                # Product data
```

## 8. Tasks cần thực hiện

### Phase 1: Setup cơ bản
- [ ] Tạo Astro project mới
- [ ] Cài đặt Polar SDK và dependencies
- [ ] Cấu hình environment variables
- [ ] Setup Cloudflare deployment

### Phase 2: Tích hợp Polar
- [ ] Tạo Polar organization và products
- [ ] Implement API để lấy products từ Polar
- [ ] Tạo webhook endpoint
- [ ] Test webhook với ngrok

### Phase 3: Frontend
- [ ] Thiết kế layout và components
- [ ] Trang danh sách sản phẩm
- [ ] Trang chi tiết sản phẩm
- [ ] Tích hợp checkout buttons

### Phase 4: Deployment & Testing
- [ ] Deploy lên Cloudflare Pages
- [ ] Cấu hình webhook URL production
- [ ] Test end-to-end flow
- [ ] Tối ưu performance và SEO

### Phase 5: Enhancements
- [ ] Thêm search và filter
- [ ] Tối ưu images
- [ ] Analytics tracking
- [ ] Error handling và monitoring

## 9. Lưu ý quan trọng

### Bảo mật
- Webhook secret phải được bảo vệ
- Validate tất cả webhook payloads
- Rate limiting cho API endpoints

### Performance
- Sử dụng Astro's image optimization
- Cache product data appropriately
- Minimize JavaScript bundle

### UX
- Loading states cho checkout
- Error handling user-friendly
- Mobile responsive design

### SEO
- Meta tags cho từng sản phẩm
- Structured data markup
- Sitemap generation

## 10. Timeline ước tính

- **Week 1**: Setup và tích hợp cơ bản
- **Week 2**: Frontend development
- **Week 3**: Testing và deployment
- **Week 4**: Polish và optimizations

## Kết luận

Dự án này sử dụng stack hiện đại và hiệu quả:
- **Astro**: Performance tốt, SEO-friendly
- **Polar.sh**: Payment processing đơn giản, phí thấp
- **Cloudflare**: Hosting nhanh, global CDN

Kiến trúc đơn giản nhưng mạnh mẽ, phù hợp cho việc bán sản phẩm số với minimal complexity.
