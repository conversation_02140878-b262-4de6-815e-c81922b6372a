globalThis.process ??= {}; globalThis.process.env ??= {};
import { W as Webhooks } from '../../chunks/index_C4LTQoxk.mjs';
export { renderers } from '../../renderers.mjs';

const prerender = false;
const POST = Webhooks({
  webhookSecret: "polar_whs_f1Gwbp88YdQuQK5pnVyFPd8YcawooY9oOMbB81N1jLU",
  onPayload: async (payload) => {
    console.log("Received webhook:", payload.type);
    try {
      switch (payload.type) {
        case "product.created":
          await handleProductCreated(payload.data);
          break;
        case "product.updated":
          await handleProductUpdated(payload.data);
          break;
        case "product.deleted":
          await handleProductDeleted(payload.data);
          break;
        case "order.paid":
          await handleOrderPaid(payload.data);
          break;
        default:
          console.log("Unhandled webhook type:", payload.type);
      }
    } catch (error) {
      console.error("Error processing webhook:", error);
      throw error;
    }
  }
});
async function handleProductCreated(product) {
  console.log("Product created:", product.id, product.name);
}
async function handleProductUpdated(product) {
  console.log("Product updated:", product.id, product.name);
}
async function handleProductDeleted(product) {
  console.log("Product deleted:", product.id);
}
async function handleOrderPaid(order) {
  console.log("Order paid:", order.id);
}

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  POST,
  prerender
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
