<!DOCTYPE html><html lang="en"> <head><title>Accessories - Polar Image Store</title><meta charset="UTF-8"><link rel="canonical" href="http://infpik.store/products/category/accessories"><meta name="description" content="Browse our collection of accessories digital images and artwork. High-quality digital assets for creative projects."><meta name="robots" content="index, follow"><meta property="og:title" content="Accessories - Polar Image Store"><meta property="og:type" content="website"><meta property="og:image" content="http://infpik.store/og-image.jpg"><meta property="og:url" content="http://infpik.store/products/category/accessories"><meta property="og:description" content="Browse our collection of accessories digital images and artwork. High-quality digital assets for creative projects."><meta property="og:locale" content="en_US"><meta property="og:site_name" content="Polar Image Store"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content="@polarimagestore"><meta name="twitter:title" content="Accessories - Polar Image Store"><meta name="twitter:image" content="http://infpik.store/og-image.jpg"><meta name="twitter:image:alt" content="Accessories - Polar Image Store - Polar Image Store"><meta name="twitter:description" content="Browse our collection of accessories digital images and artwork. High-quality digital assets for creative projects."><meta name="twitter:creator" content="@polarimagestore"><link rel="icon" type="image/svg+xml" href="/favicon.svg"><link rel="sitemap" href="/sitemap-index.xml"><link rel="canonical" href="http://infpik.store/products/category/accessories"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="Astro v5.12.4"><meta name="robots" content="index, follow"><meta name="googlebot" content="index, follow"><meta name="theme-color" content="#6366f1"><meta name="msapplication-TileColor" content="#6366f1"><link rel="stylesheet" href="/_astro/about.BlT0G9wR.css"></head> <body class="min-h-screen flex flex-col"> <header class="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-primary-100 py-4"> <div class="container"> <nav class="flex items-center justify-between"> <!-- Logo --> <a href="/" class="flex items-center gap-2 text-xl font-bold text-primary-900 hover:text-accent-600 transition-colors"> <img src="/logo.svg" alt="Logo" loading="lazy" decoding="async" fetchpriority="auto" width="32" height="32" class="w-8 h-8 text-accent-600">
InfPik
</a> <!-- Mobile Search Bar --> <div id="mobileHeaderSearchContainer" class="md:hidden relative flex-1 mx-2 opacity-0 transform -translate-y-2 transition-all duration-300 ease-in-out"> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"> <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> <input type="text" id="mobileProductSearch" class="block w-full pl-8 pr-4 py-1.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200 text-sm" placeholder="Search tags..." autocomplete="off"> </div> <!-- Search Bar (Desktop) --> <div id="headerSearchContainer" class="hidden md:flex flex-1 max-w-md mx-8 opacity-0 transform -translate-y-2 transition-all duration-300 ease-in-out"> <div class="relative w-full"> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"> <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> <input type="text" id="productSearch" class="block w-full pl-10 pr-4 py-2.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200" placeholder="Search tags..." autocomplete="off"> <!-- Search results dropdown (hidden by default) --> <div id="searchResults" class="absolute top-full left-0 right-0 mt-1 bg-white border border-primary-200 rounded-xl shadow-lg z-50 hidden max-h-96 overflow-y-auto"> <!-- Search results will be populated here --> </div> </div> </div> <!-- CTA Button & Mobile Menu --> <div class="flex items-center gap-4"> <a href="/products" class="btn-primary hidden md:inline-flex">
Browse Collection
</a> <!-- Mobile menu button --> <button class="md:hidden p-2 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full transition-all" id="mobile-menu-button"> <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path> </svg> </button> </div> </nav> <!-- Mobile menu --> <div class="md:hidden hidden" id="mobile-menu"> <div class="pt-4 pb-2 border-t border-primary-100 mt-4"> <!-- Mobile Navigation --> <ul class="space-y-2"> <li><a href="/" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Home</a></li> <li><a href="/products" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Products</a></li> <li><a href="/about" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">About</a></li> </ul> <!-- Legal Links --> <div class="mt-4 pt-4 border-t border-primary-100"> <p class="px-4 text-xs uppercase text-primary-500 font-medium mb-2">Legal</p> <ul class="space-y-2"> <li><a href="/privacy" class="block py-2 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all text-sm">Privacy Policy</a></li> <li><a href="/terms" class="block py-2 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all text-sm">Terms of Service</a></li> </ul> </div> <div class="mt-4 pt-4 border-t border-primary-100"> <a href="/products" class="btn-primary w-full justify-center">
Browse Collection
</a> </div> </div> </div> </div> </header> <main class="flex-1 pb-12">   <script type="application/ld+json">{"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Home","item":"http://infpik.store"},{"@type":"ListItem","position":2,"name":"Products","item":"http://infpik.store/products"},{"@type":"ListItem","position":3,"name":"Accessories","item":"http://infpik.store/products/category/accessories"}]}</script> <div class="container mx-auto px-4 py-8"> <section class="text-center mb-12"> <h1 class="text-4xl font-bold text-gray-900 mb-4">Accessories</h1> <p class="text-xl text-gray-600 max-w-2xl mx-auto"> Discover 1 accessories item in our collection </p> </section> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"> <div class="group bg-white rounded-3xl overflow-hidden shadow-sm border border-primary-100 transition-all duration-500 hover:-translate-y-3 hover:shadow-2xl hover:shadow-primary-500/10"> <div class="relative aspect-[4/3] overflow-hidden bg-gradient-to-br from-primary-50 to-accent-50"> <img src="https://polar-public-files.s3.amazonaws.com/product_media/e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca/e2f653ac-d47f-4955-a077-9def34234897/3d%20in-ear%20icon.png" alt="3D in-ear headphone icon" loading="eager" fetchpriority="high" decoding="async" width="800" height="600" class="w-full h-full object-cover transition-all duration-500 group-hover:scale-110"> <!-- Gradient overlay --> <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div> <!-- Hover actions --> <div class="absolute inset-0 flex items-center justify-center gap-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-4 group-hover:translate-y-0"> <a href="/products/3d-in-ear-headphone-icon" class="flex items-center gap-2 px-6 py-3 bg-white/95 backdrop-blur-sm text-primary-900 rounded-full font-semibold text-sm transition-all duration-200 hover:bg-white hover:scale-105 hover:shadow-lg"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path> </svg>
Preview
</a> <a href="/api/checkout?products=a5955a3d-8e30-4855-ad8a-0bec98f30a6a" class="flex items-center gap-2 px-6 py-3 bg-accent-600 text-white rounded-full font-semibold text-sm transition-all duration-200 hover:bg-accent-700 hover:scale-105 hover:shadow-lg"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path> </svg>
Buy Now
</a> </div> <!-- Price badge --> <div class="absolute top-4 right-4 px-3 py-1.5 bg-white/95 backdrop-blur-sm rounded-full"> <span class="text-lg font-bold text-primary-900">$1.00</span> </div> </div> <div class="p-6"> <!-- Category badge --> <div class="mb-3"> <span class="inline-flex items-center px-3 py-1 bg-accent-100 text-accent-700 text-xs font-medium rounded-full"> headphone </span> </div> <!-- Title --> <h3 class="text-xl font-bold text-primary-900 mb-2 line-clamp-2 group-hover:text-accent-600 transition-colors"> 3D in-ear headphone icon </h3> <!-- Description --> <p class="text-primary-600 text-sm mb-4 line-clamp-2 leading-relaxed"> 3D Illustration in-ear headphone icon gives your project awesome illustration, this icon you can use for UI UX design, mobile apps, web infographics, and many more. </p> <!-- Footer --> <div class="flex items-center justify-between pt-4 border-t border-primary-100"> <div class="flex items-center gap-2 text-xs text-primary-500"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path> </svg> <span>Digital Download</span> </div> <a href="/products/3d-in-ear-headphone-icon" class="text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors">
View Details →
</a> </div> </div> </div> </div> </div>  </main> <footer class="bg-white border-t border-primary-100 py-12 text-primary-600"> <div class="container"> <div class="text-center"> <div class="flex items-center justify-center gap-2 text-lg font-semibold text-primary-900 mb-4"> <img src="/logo.svg" alt="Logo" loading="lazy" decoding="async" fetchpriority="auto" width="24" height="24" class="w-6 h-6 text-accent-600">
InfPik
</div> <div class="flex justify-center gap-4 mb-4"> <a href="/about" class="text-sm hover:text-accent-600 transition-colors">About Us</a> <a href="/privacy" class="text-sm hover:text-accent-600 transition-colors">Privacy Policy</a> <a href="/terms" class="text-sm hover:text-accent-600 transition-colors">Terms of Service</a> </div> <p class="text-sm">&copy; 2025 Polar Image Store. All rights reserved.</p> </div> </div> </footer> <script type="module" src="/_astro/Layout.astro_astro_type_script_index_0_lang.D2CKeYby.js"></script></body></html>