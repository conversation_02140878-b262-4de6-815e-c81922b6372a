---
import Layout from '../layouts/Layout.astro';
import Hero from '../components/Hero.astro';
import ProductCard from '../components/ProductCard.astro';
import StructuredData from '../components/StructuredData.astro';
import { createPolarClient, transformPolarProduct, generateCategoriesWithCounts, generateTagsWithCounts } from '../utils/polar';
import type { LocalProduct } from '../types/polar';

// Fetch featured products for homepage
let featuredProducts: LocalProduct[] = [];
let categories: Array<{id: string, name: string, count: number}> = [];
let tags: Array<{id: string, name: string, count: number}> = [];
let error: string | null = null;

try {
  const polar = createPolarClient();
  const organizationId = import.meta.env.POLAR_ORGANIZATION_ID;

  if (organizationId) {
    const response = await polar.products.list({
      organizationId,
      isArchived: false
      // Remove limit to get all products for proper filtering
    });

    const productList = response.result?.items || [];
    featuredProducts = productList.map(transformPolarProduct).filter((product): product is LocalProduct => product !== null);

    // Generate categories and tags from all products for navigation
    categories = generateCategoriesWithCounts(featuredProducts);
    tags = generateTagsWithCounts(featuredProducts);
  } else {
    error = 'Organization ID not configured';
  }
} catch (e) {
  console.error('Error fetching featured products:', e);
  error = 'Failed to load featured products';
}
---

<Layout
  title="Polar Image Store - Premium Digital Images & Artwork"
  description="Discover premium digital images and artwork for your creative projects. High-quality, commercial-use digital assets available for instant download."
  type="website"
>
  <!-- Organization Structured Data -->
  <StructuredData type="Organization" data={{}} />

  <!-- Website Structured Data -->
  <StructuredData type="WebSite" data={{}} />
  <!-- Hero Section -->
  <Hero
    title="Premium. Passion. Creativity."
  >
    <!-- Category Navigation -->
    <Fragment slot="category-navigation">
      {categories.map((category) => (
        <button
          class={`category-tab flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all whitespace-nowrap ${
            category.id === 'all'
              ? 'bg-accent-600 text-white shadow-md'
              : 'bg-primary-50 text-primary-700 hover:bg-primary-100 hover:text-primary-900'
          }`}
          data-category={category.id}
        >
          {category.name}
          {category.count && (
            <span class={`text-xs px-2 py-0.5 rounded-full ${
              category.id === 'all'
                ? 'bg-white/20 text-white'
                : 'bg-primary-200 text-primary-600'
            }`}>
              {category.count}
            </span>
          )}
        </button>
      ))}
    </Fragment>

    <!-- Tag Navigation -->
    <Fragment slot="tag-navigation">
      <div class="text-center">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Popular Tags</h3>
        {tags.length > 1 ? (
          <div class="flex flex-wrap gap-2 justify-center max-w-3xl mx-auto">
            {tags.slice(1, 11).map((tag) => (
              <button
                class="tag-tab inline-flex items-center gap-1.5 px-3 py-1.5 bg-white text-gray-700 border border-gray-300 rounded-full text-xs font-medium transition-all hover:bg-gray-100 hover:text-gray-900"
                data-tag={tag.id}
              >
                #{tag.name}
                <span class="text-xs px-1.5 py-0.5 bg-gray-200 text-gray-600 rounded-full">
                  {tag.count}
                </span>
              </button>
            ))}
          </div>
        ) : (
          <div class="text-gray-500 text-sm">
            <p>No tags available yet. Add tags to your products on Polar.sh:</p>
            <code class="bg-gray-100 px-2 py-1 rounded text-xs mt-2 inline-block">
              Key: tags, Value: tag1,tag2,tag3
            </code>
          </div>
        )}
      </div>
    </Fragment>
  </Hero>

  <!-- Featured Products Section -->
  <section class="py-16 bg-white">
    <div class="container">
      <div class="text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-bold text-primary-900 mb-4">Featured Collections</h2>
        <p class="text-lg text-primary-600 max-w-2xl mx-auto">
          Discover our most popular digital images and artwork, carefully curated for quality and creativity
        </p>
      </div>

      {error ? (
        <div class="text-center py-16">
          <div class="inline-flex items-center gap-3 text-warning-600 bg-warning-50 px-6 py-4 rounded-xl border border-warning-200">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span class="font-medium">{error}</span>
          </div>
        </div>
      ) : featuredProducts.length === 0 ? (
        <div class="text-center py-16">
          <div class="inline-flex items-center gap-3 text-primary-600">
            <svg class="animate-spin w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <span>Loading featured products...</span>
          </div>
        </div>
      ) : (
        <div id="products-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {featuredProducts.map((product, index) => (
            <div
              class="product-item"
              data-category={product.category || 'uncategorized'}
              data-name={product.name.toLowerCase()}
              data-description={product.description.toLowerCase()}
              data-tags={product.tags?.join(',').toLowerCase() || ''}
              style={index >= 16 ? 'display: none;' : ''}
              data-featured={index < 16 ? 'true' : 'false'}
            >
              <ProductCard product={product} />
            </div>
          ))}
        </div>
      )}

      {featuredProducts.length > 0 && (
        <div class="text-center mt-12">
          <a href="/products" id="view-all-products-btn" class="btn-primary text-lg px-8 py-4">
            View All Products
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </a>
        </div>
      )}
    </div>
  </section>

  <!-- Features Section -->
  <section class="py-16 bg-primary-50">
    <div class="container">
      <div class="text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-bold text-primary-900 mb-4">Why Choose Our Images?</h2>
        <p class="text-lg text-primary-600 max-w-2xl mx-auto">
          Professional quality, instant access, and commercial licensing for all your creative projects
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="text-center p-8 bg-white rounded-3xl border border-primary-100 shadow-sm hover:shadow-xl hover:-translate-y-2 transition-all duration-300">
          <div class="w-16 h-16 bg-gradient-to-br from-accent-500 to-success-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
          </div>
          <h3 class="text-xl font-bold text-primary-900 mb-4">Premium Quality</h3>
          <p class="text-primary-600 leading-relaxed">Professional-grade digital images in ultra-high resolution for stunning results</p>
        </div>

        <div class="text-center p-8 bg-white rounded-3xl border border-primary-100 shadow-sm hover:shadow-xl hover:-translate-y-2 transition-all duration-300">
          <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 class="text-xl font-bold text-primary-900 mb-4">Instant Access</h3>
          <p class="text-primary-600 leading-relaxed">Download your images immediately after purchase with secure, lifetime access</p>
        </div>

        <div class="text-center p-8 bg-white rounded-3xl border border-primary-100 shadow-sm hover:shadow-xl hover:-translate-y-2 transition-all duration-300">
          <div class="w-16 h-16 bg-gradient-to-br from-warning-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
            </svg>
          </div>
          <h3 class="text-xl font-bold text-primary-900 mb-4">Commercial License</h3>
          <p class="text-primary-600 leading-relaxed">Full commercial rights included - use for personal and business projects without limits</p>
        </div>
      </div>
    </div>
  </section>
</Layout>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Handle category change events from category navigation in Hero
    window.addEventListener('categoryChange', (event) => {
      const categoryId = event.detail.categoryId;

      // Apply filtering on homepage instead of redirecting
      filterProducts(categoryId);
    });

    // Filter products function
    function filterProducts(category) {
      const productItems = document.querySelectorAll('.product-item');
      let visibleCount = 0;

      productItems.forEach(item => {
        const productCategory = item.dataset.category;
        const isFeatured = item.dataset.featured === 'true';

        let categoryMatch = category === 'all' || productCategory === category;

        if (categoryMatch) {
          // Show all matching products when filtering by category
          // Show only featured products when showing "All"
          if (category === 'all' && !isFeatured) {
            item.style.display = 'none';
          } else {
            item.style.display = 'block';
            visibleCount++;
          }
        } else {
          item.style.display = 'none';
        }
      });

      // Update the "View All Products" button text to reflect filtered results
      const viewAllButton = document.querySelector('#view-all-products-btn');
      if (viewAllButton && category !== 'all') {
        const categoryName = getCategoryDisplayName(category);
        viewAllButton.innerHTML = `
          View All ${categoryName} Products
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
          </svg>
        `;
        viewAllButton.href = `/products/category/${category}`;
      } else if (viewAllButton && category === 'all') {
        viewAllButton.innerHTML = `
          View All Products
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
          </svg>
        `;
        viewAllButton.href = '/products';
      }
    }

    // Helper function to convert category slug to display name
    function getCategoryDisplayName(category) {
      return category
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    }
  });
</script>

